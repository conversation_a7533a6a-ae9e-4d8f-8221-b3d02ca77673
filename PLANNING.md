## **PROMPT: SaaS Platform Development - Database & App Provisioning**

### **PROJECT REQUIREMENTS**
Develop a complete SaaS platform that allows users to rent and manage databases and applications with automated Docker provisioning system. Use Supabase as backend and Tripay as payment gateway.

### **TECHNOLOGY STACK**
- **Backend**: Supabase (PostgreSQL, Auth, Realtime, Edge Functions)
- **Frontend**: React/Next.js with TypeScript
- **Payment Gateway**: Tripay (Indonesian payment gateway)
- **Containerization**: Docker & Docker Compose
- **Database**: PostgreSQL (via Supabase)
- **Real-time**: Supabase Realtime subscriptions
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage

### **DATABASE SCHEMA DESIGN**
Create complete Supabase database schema with the following tables:

1. **profiles** (extends auth.users)
   - User profile information, balance, role
   - Include phone number for Indonesian users

2. **tripay_transactions**
   - Tripay payment tracking with reference, merchant_ref, amount, fee
   - Payment method, status, pay_code, pay_url, expiry time

3. **transactions**
   - Internal transaction logs (topup, deduction, refund)
   - Link to tripay_transactions for payment source

4. **service_templates**
   - Available services (PostgreSQL, MySQL, MongoDB, n8n, WordPress)
   - Pricing in IDR, Docker configurations, resource specs

5. **service_instances**
   - User's active services with status, container info, endpoints
   - Link to servers and templates

6. **servers**
   - Docker host servers management
   - Resource monitoring, regions, status

7. **service_links**
   - Application to database connections
   - Connection configurations

Include proper Row Level Security (RLS) policies for multi-tenant isolation.

### **USER FLOW IMPLEMENTATION**

#### **Authentication & Registration**
- Email-based registration with Supabase Auth
- Profile completion with phone number (required for Tripay)
- Email verification process
- Password reset functionality

#### **Balance Management with Tripay**
- Top-up interface with Indonesian payment methods:
  - Virtual Accounts (BCA, Mandiri, BRI, BNI, BSI)
  - E-Wallets (OVO, DANA, ShopeePay, LinkAja)
  - QRIS
  - Retail (Alfamart, Indomaret)
- Real-time balance updates using Supabase Realtime
- Transaction history with Tripay reference tracking
- Auto-deduction system for service usage

#### **Service Ordering System**
- Service catalog with pricing in IDR
- Resource specification (CPU, RAM, Storage)
- Database options: PostgreSQL, MySQL/MariaDB, MongoDB
- Application options: n8n, WordPress, custom apps
- Service linking system (connect apps to databases)
- One-click deployment with status tracking

#### **Provisioning Engine**
- Automated Docker container creation via Edge Functions
- Multi-server deployment support
- Real-time status updates (pending → provisioning → running)
- Automatic credential generation and delivery
- SSL certificate management
- Custom domain support

### **ADMIN PANEL FEATURES**

#### **User Management**
- Complete user CRUD operations
- Balance adjustment capabilities
- Usage analytics and reports
- Account suspension/activation
- Bulk operations support

#### **Service Management**
- Add/edit/remove service templates
- Version management for applications
- Docker image configurations
- Resource limit settings
- Pricing management in IDR

#### **Server Management**
- Multiple Docker host management
- Health monitoring and alerts
- Resource utilization tracking
- Load balancing configuration
- Regional server deployment

#### **System Configuration**
- Global system settings
- Email notification templates
- Tripay payment method configuration
- Security settings and API keys
- Audit log viewing

### **TRIPAY INTEGRATION REQUIREMENTS**

#### **Payment Flow**
- Create payment transactions with proper Tripay signature
- Support all major Indonesian payment methods
- Handle payment callbacks and webhooks securely
- Implement payment status checking
- Auto-expire handling for unpaid transactions

#### **Security Implementation**
- Proper signature verification for callbacks
- Secure API key management
- Webhook endpoint protection
- Transaction idempotency

### **SUPABASE EDGE FUNCTIONS**
Develop the following Edge Functions:

1. **create-tripay-payment**: Handle payment creation with Tripay API
2. **tripay-callback**: Process payment callbacks and update balances
3. **provision-service**: Manage Docker container provisioning
4. **billing-engine**: Hourly usage calculation and deduction
5. **health-monitor**: Service health checking and alerts
6. **cleanup-expired**: Clean up expired services and payments

### **REAL-TIME FEATURES**
Implement Supabase Realtime subscriptions for:
- Service provisioning status updates
- Balance changes
- Payment status updates
- System notifications
- Admin dashboard metrics

### **SECURITY & PERFORMANCE**
- Implement comprehensive RLS policies
- Rate limiting for API endpoints
- Input validation and sanitization
- Docker container isolation
- Network security for provisioned services
- Regular security updates automation
- Database connection pooling
- Caching strategies for frequently accessed data

### **MONITORING & LOGGING**
- Service uptime monitoring
- Resource usage tracking
- Payment transaction logging
- Error logging and alerting
- Performance metrics
- User activity tracking
- Audit trail for admin actions

### **RESPONSIVE UI/UX DESIGN**
- Modern, mobile-first design
- Indonesian language support (Bahasa Indonesia)
- Dark/light theme toggle
- Intuitive dashboard with real-time data
- Service management interface
- Payment flow optimization for Indonesian users
- Loading states and error handling
- Toast notifications for actions

### **DEPLOYMENT & INFRASTRUCTURE**
- Supabase project setup and configuration
- Environment variables management
- Docker server infrastructure setup
- Tripay webhook endpoint configuration
- Domain and SSL certificate management
- Backup and disaster recovery procedures
- CI/CD pipeline setup

### **TESTING REQUIREMENTS**
- Unit tests for critical functions
- Integration tests for Tripay payments
- End-to-end testing for user flows
- Load testing for provisioning system
- Security testing for vulnerabilities

### **DOCUMENTATION**
- API documentation
- User manual in Bahasa Indonesia
- Admin guide
- Deployment guide
- Troubleshooting guide

**OUTPUT EXPECTATION**: Provide complete, production-ready code with proper error handling, security implementations, and comprehensive documentation. The system should be scalable, maintainable, and optimized for Indonesian market requirements.

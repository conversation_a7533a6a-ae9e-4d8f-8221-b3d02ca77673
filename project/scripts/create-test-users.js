#!/usr/bin/env node

/**
 * <PERSON>ript to create test users for CloudHost ID
 * This script creates admin and regular user accounts for testing
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '../.env.local') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please check your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createTestUsers() {
  console.log('🚀 Creating test users for CloudHost ID...\n');

  try {
    // Create admin user
    console.log('👤 Creating admin user...');
    const { data: adminData, error: adminError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'admin123456',
      options: {
        data: {
          full_name: 'CloudHost Admin'
        }
      }
    });

    if (adminError) {
      if (adminError.message.includes('already registered')) {
        console.log('ℹ️  Admin user already exists');
      } else {
        console.error('❌ Admin creation error:', adminError.message);
      }
    } else {
      console.log('✅ Admin user created successfully');
      
      // Update admin profile
      if (adminData.user) {
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({ 
            id: adminData.user.id,
            email: '<EMAIL>',
            full_name: 'CloudHost Admin',
            phone: '+6281234567890',
            role: 'admin',
            balance: 0,
            status: 'active'
          });
        
        if (profileError) {
          console.error('❌ Error updating admin profile:', profileError.message);
        } else {
          console.log('✅ Admin profile updated');
        }
      }
    }

    // Create regular user
    console.log('\n👤 Creating regular user...');
    const { data: userData, error: userError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'user123456',
      options: {
        data: {
          full_name: 'Test User'
        }
      }
    });

    if (userError) {
      if (userError.message.includes('already registered')) {
        console.log('ℹ️  Regular user already exists');
      } else {
        console.error('❌ User creation error:', userError.message);
      }
    } else {
      console.log('✅ Regular user created successfully');
      
      // Update user profile
      if (userData.user) {
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({ 
            id: userData.user.id,
            email: '<EMAIL>',
            full_name: 'Test User',
            phone: '+6281234567891',
            role: 'user',
            balance: 100000, // 100k IDR for testing
            status: 'active'
          });
        
        if (profileError) {
          console.error('❌ Error updating user profile:', profileError.message);
        } else {
          console.log('✅ User profile updated');
          
          // Add initial transaction
          const { error: transactionError } = await supabase
            .from('transactions')
            .insert({
              user_id: userData.user.id,
              type: 'topup',
              amount: 100000,
              balance_before: 0,
              balance_after: 100000,
              description: 'Initial balance for testing'
            });
          
          if (transactionError) {
            console.error('❌ Error creating initial transaction:', transactionError.message);
          } else {
            console.log('✅ Initial transaction created');
          }
          
          // Add welcome notification
          const { error: notificationError } = await supabase
            .from('notifications')
            .insert({
              user_id: userData.user.id,
              type: 'success',
              title: 'Selamat Datang!',
              message: 'Akun Anda telah berhasil dibuat. Selamat menggunakan CloudHost ID!',
              data: {},
              read: false
            });
          
          if (notificationError) {
            console.error('❌ Error creating notification:', notificationError.message);
          } else {
            console.log('✅ Welcome notification created');
          }
        }
      }
    }

    console.log('\n🎉 Test users setup completed!');
    console.log('\n📋 Test Credentials:');
    console.log('Admin: <EMAIL> / admin123456');
    console.log('User:  <EMAIL> / user123456');
    console.log('\n🌐 You can now test login at: http://localhost:5173/auth');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    process.exit(1);
  }
}

// Test login function
async function testLogin(email, password) {
  console.log(`\n🔐 Testing login for ${email}...`);
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error) {
    console.error(`❌ Login failed for ${email}:`, error.message);
    return false;
  } else {
    console.log(`✅ Login successful for ${email}`);
    
    // Fetch user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();

    if (profileError) {
      console.error('❌ Error fetching profile:', profileError.message);
    } else {
      console.log(`👤 Profile: ${profile.full_name} (${profile.role})`);
      console.log(`💰 Balance: Rp ${profile.balance.toLocaleString('id-ID')}`);
    }
    
    // Sign out
    await supabase.auth.signOut();
    return true;
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--test-login')) {
    console.log('🧪 Testing login functionality...\n');
    
    const adminSuccess = await testLogin('<EMAIL>', 'admin123456');
    const userSuccess = await testLogin('<EMAIL>', 'user123456');
    
    if (adminSuccess && userSuccess) {
      console.log('\n✅ All login tests passed!');
    } else {
      console.log('\n❌ Some login tests failed. Please check the users exist.');
      process.exit(1);
    }
  } else {
    await createTestUsers();
  }
}

main().catch(console.error);

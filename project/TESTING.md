# CloudHost ID - Testing Guide

## 🧪 Test Users & Authentication

### Test Credentials

Berikut adalah kredensial untuk testing yang sudah dibuat:

#### Admin User
- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Role**: Admin
- **Balance**: Rp 0
- **Phone**: +6281234567890

#### Regular User
- **Email**: `<EMAIL>`
- **Password**: `user123456`
- **Role**: User
- **Balance**: Rp 100,000
- **Phone**: +6281234567891

### 🚀 Quick Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Create Test Users** (jika belum ada)
   ```bash
   npm run create-test-users
   ```

3. **Test Login Functionality**
   ```bash
   npm run test-login
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

5. **Access Application**
   - Frontend: http://localhost:5173
   - Login Page: http://localhost:5173/auth

### 🔐 Testing Authentication

#### Manual Testing via Web Interface

1. **Buka halaman login**: http://localhost:5173/auth
2. **Test Admin Login**:
   - Email: `<EMAIL>`
   - Password: `admin123456`
   - Seharusnya redirect ke dashboard admin
3. **Test User Login**:
   - Email: `<EMAIL>`
   - Password: `user123456`
   - Seharusnya redirect ke dashboard user

#### Automated Testing via Script

```bash
# Test login functionality
npm run test-login
```

Script ini akan:
- Test login untuk admin dan user
- Menampilkan profile information
- Menampilkan balance
- Verify role permissions

### 📊 Sample Data

Database sudah diisi dengan sample data berikut:

#### Servers
- **Jakarta Server 1**: 8 cores, 32GB RAM, 500GB storage
- **Singapore Server 1**: 16 cores, 64GB RAM, 1TB storage  
- **Surabaya Server 1**: 4 cores, 16GB RAM, 250GB storage

#### Service Templates
- **MySQL 8.0**: Database server (Rp 1,500/hour)
- **PostgreSQL 15**: Database server (Rp 1,600/hour)
- **Redis 7**: Cache server (Rp 800/hour)
- **WordPress**: CMS application (Rp 1,800/hour)

#### System Metrics
- Sample metrics untuk monitoring dashboard
- CPU, memory, storage usage data
- Request counts dan response times

#### Notifications
- Welcome notification untuk user
- Admin access notification untuk admin

### 🛠️ Troubleshooting

#### Login Issues

1. **"Email not confirmed"**
   - User emails sudah dikonfirmasi otomatis
   - Jika masih error, jalankan:
   ```sql
   UPDATE auth.users 
   SET email_confirmed_at = NOW() 
   WHERE email IN ('<EMAIL>', '<EMAIL>');
   ```

2. **"Row Level Security policy violation"**
   - RLS policies sudah dikonfigurasi
   - User bisa insert/update profile mereka sendiri
   - Admin bisa akses semua data

3. **"Rate limiting"**
   - Supabase membatasi signup requests
   - Tunggu beberapa detik sebelum retry
   - User sudah dibuat jika muncul error ini

#### Database Issues

1. **Check if users exist**:
   ```sql
   SELECT id, email, created_at FROM auth.users;
   ```

2. **Check profiles**:
   ```sql
   SELECT * FROM profiles;
   ```

3. **Reset test data**:
   ```bash
   npm run create-test-users
   ```

### 🔍 Testing Checklist

- [ ] Admin dapat login dengan kredensial yang benar
- [ ] User dapat login dengan kredensial yang benar
- [ ] Admin memiliki akses ke halaman admin
- [ ] User tidak dapat akses halaman admin
- [ ] Profile data ditampilkan dengan benar
- [ ] Balance ditampilkan dengan benar
- [ ] Notifications muncul untuk user baru
- [ ] Dashboard menampilkan data sample
- [ ] Service templates tersedia
- [ ] Server data ditampilkan

### 📝 Notes

- Password menggunakan format sederhana untuk testing
- Email confirmation dibypass untuk development
- Sample data direset setiap kali script dijalankan
- RLS policies dikonfigurasi untuk security
- Rate limiting berlaku untuk signup requests

### 🚨 Security Notes

**PENTING**: Kredensial ini hanya untuk development/testing!

- Jangan gunakan password ini di production
- Ganti semua kredensial sebelum deploy
- Enable email confirmation di production
- Review dan update RLS policies sesuai kebutuhan

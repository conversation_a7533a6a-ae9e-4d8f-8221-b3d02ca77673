-- CloudHost ID Row Level Security Setup
-- Run this script in Supabase SQL Editor AFTER running database_setup.sql

-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tripay_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_alerts ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
R<PERSON>URNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get current user profile
CREATE OR REPLACE FUNCTION get_user_profile()
RETURNS public.profiles AS $$
DECLARE
    profile public.profiles;
BEGIN
    SELECT * INTO profile FROM public.profiles WHERE id = auth.uid();
    RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can insert profiles" ON public.profiles
    FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admins can delete profiles" ON public.profiles
    FOR DELETE USING (is_admin());

-- Service templates policies
CREATE POLICY "Anyone can view active templates" ON public.service_templates
    FOR SELECT USING (status = 'active');

CREATE POLICY "Admins can manage all templates" ON public.service_templates
    FOR ALL USING (is_admin());

-- Servers policies
CREATE POLICY "Admins can manage all servers" ON public.servers
    FOR ALL USING (is_admin());

-- Service instances policies
CREATE POLICY "Users can view own services" ON public.service_instances
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own services" ON public.service_instances
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own services" ON public.service_instances
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own services" ON public.service_instances
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all services" ON public.service_instances
    FOR ALL USING (is_admin());

-- Service links policies
CREATE POLICY "Users can view own service links" ON public.service_links
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own service links" ON public.service_links
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all service links" ON public.service_links
    FOR ALL USING (is_admin());

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all transactions" ON public.transactions
    FOR ALL USING (is_admin());

-- Tripay transactions policies
CREATE POLICY "Users can view own tripay transactions" ON public.tripay_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tripay transactions" ON public.tripay_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all tripay transactions" ON public.tripay_transactions
    FOR ALL USING (is_admin());

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all notifications" ON public.notifications
    FOR ALL USING (is_admin());

-- System metrics policies (admin only)
CREATE POLICY "Admins can manage system metrics" ON public.system_metrics
    FOR ALL USING (is_admin());

-- System alerts policies
CREATE POLICY "Users can view own alerts" ON public.system_alerts
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own alerts" ON public.system_alerts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all alerts" ON public.system_alerts
    FOR ALL USING (is_admin());

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update user balance
CREATE OR REPLACE FUNCTION public.update_user_balance(
    user_id UUID,
    amount DECIMAL(15,2),
    transaction_type transaction_type,
    description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_balance DECIMAL(15,2);
    new_balance DECIMAL(15,2);
BEGIN
    -- Get current balance
    SELECT balance INTO current_balance 
    FROM public.profiles 
    WHERE id = user_id;
    
    IF current_balance IS NULL THEN
        RAISE EXCEPTION 'User not found';
    END IF;
    
    -- Calculate new balance
    IF transaction_type IN ('topup', 'refund', 'adjustment') THEN
        new_balance := current_balance + amount;
    ELSE
        new_balance := current_balance - amount;
        
        -- Check if user has sufficient balance for deductions
        IF new_balance < 0 AND transaction_type = 'deduction' THEN
            RAISE EXCEPTION 'Insufficient balance';
        END IF;
    END IF;
    
    -- Update balance
    UPDATE public.profiles 
    SET balance = new_balance, updated_at = NOW()
    WHERE id = user_id;
    
    -- Create transaction record
    INSERT INTO public.transactions (user_id, type, amount, balance_before, balance_after, description)
    VALUES (user_id, transaction_type, amount, current_balance, new_balance, description);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

# CloudHost ID - Supabase Setup Guide

## 🚀 Setup Supabase Cloud

### 1. Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign up/Login to your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: CloudHost ID
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
6. Click "Create new project"

### 2. Get Project Credentials
After project creation, go to **Settings > API**:
- **Project URL**: `https://your-project-ref.supabase.co`
- **Project API Keys**:
  - `anon` `public` key (for client-side)
  - `service_role` `secret` key (for server-side)

### 3. Update Environment Variables
Update `.env.local` file:
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Tripay Configuration (Sandbox)
VITE_TRIPAY_MERCHANT_CODE=T1234
VITE_TRIPAY_API_KEY=your_tripay_api_key_here
VITE_TRIPAY_PRIVATE_KEY=your_tripay_private_key_here
VITE_TRIPAY_SANDBOX=true
```

### 4. Setup Database Schema
Run the following SQL scripts in **Supabase SQL Editor**:

#### Step 1: Run `database_setup.sql`
```sql
-- Copy and paste content from database_setup.sql
-- This creates all tables, types, indexes, and triggers
```

#### Step 2: Run `database_rls.sql` 
```sql
-- Copy and paste content from database_rls.sql
-- This sets up Row Level Security policies
```

### 5. Configure Edge Functions
The following Edge Functions are already deployed:

1. **provision-service**: Handles service provisioning
2. **tripay-webhook**: Processes Tripay payment webhooks
3. **create-tripay-transaction**: Creates Tripay payment transactions
4. **system-monitor**: Collects system metrics and alerts

#### Set Environment Variables for Edge Functions
Go to **Settings > Edge Functions** and add:
```
TRIPAY_MERCHANT_CODE=your_merchant_code
TRIPAY_API_KEY=your_api_key
TRIPAY_PRIVATE_KEY=your_private_key
TRIPAY_SANDBOX=true
APP_URL=http://localhost:5173
```

### 6. Setup Authentication
Go to **Authentication > Settings**:

#### Site URL Configuration
- **Site URL**: `http://localhost:5173` (development)
- **Redirect URLs**: 
  - `http://localhost:5173/**`
  - `https://your-domain.com/**` (production)

#### Email Templates (Optional)
Customize email templates for:
- Confirm signup
- Reset password
- Magic link

### 7. Create Admin User
1. Go to **Authentication > Users**
2. Click "Add user"
3. Fill in admin details:
   - **Email**: <EMAIL>
   - **Password**: Generate strong password
   - **Email Confirm**: true
4. After creation, update user role in database:
```sql
UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### 8. Test Database Connection
Run this query in SQL Editor to verify setup:
```sql
-- Check tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check service templates
SELECT name, type, category, status FROM service_templates;

-- Check servers
SELECT name, region, status FROM servers;
```

## 🔧 Development Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Start Development Server
```bash
npm run dev
```

### 3. Access Application
- **Frontend**: http://localhost:5173
- **Supabase Dashboard**: https://supabase.com/dashboard/project/your-project-ref

## 📊 Database Schema Overview

### Core Tables
- **profiles**: User profiles extending auth.users
- **service_templates**: Available service templates
- **servers**: Docker host servers
- **service_instances**: User service instances
- **service_links**: Application-database connections

### Payment Tables
- **transactions**: Balance transactions
- **tripay_transactions**: Tripay payment records

### Monitoring Tables
- **system_metrics**: System performance metrics
- **system_alerts**: System alerts and notifications
- **notifications**: User notifications

### Key Features
- **Row Level Security**: Multi-tenant data isolation
- **Real-time Subscriptions**: Live data updates
- **Edge Functions**: Serverless backend logic
- **Automatic Triggers**: Updated timestamps and user creation

## 🔐 Security Features

### Row Level Security Policies
- Users can only access their own data
- Admins have full access to all data
- Service templates are publicly readable
- System metrics are admin-only

### Authentication
- JWT-based authentication
- Email confirmation required
- Password reset functionality
- Role-based access control

### API Security
- CORS headers configured
- Request validation
- Error handling
- Rate limiting (via Supabase)

## 🚀 Production Deployment

### 1. Update Environment Variables
```env
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-production-anon-key
VITE_TRIPAY_SANDBOX=false
VITE_APP_URL=https://your-domain.com
```

### 2. Configure Production Settings
- Update Site URL in Supabase Auth settings
- Set up custom domain (optional)
- Configure email templates
- Set up monitoring and alerts

### 3. Deploy Edge Functions
Edge Functions are already deployed and will work in production with proper environment variables.

## 📞 Support

For issues or questions:
1. Check Supabase logs in Dashboard
2. Review Edge Function logs
3. Check browser console for client-side errors
4. Verify environment variables are set correctly

## 🔄 Database Migrations

Future schema changes should be applied as migrations:
1. Create new migration file
2. Test in development
3. Apply to production via Supabase Dashboard
4. Update TypeScript types if needed

-- Seed data for CloudHost ID SaaS Platform
-- This file creates test users (admin and regular user) for development and testing

-- Note: This seed file is designed to work with Supabase local development
-- For production, users should be created through the application interface

-- First, let's create the test users in auth.users table
-- We'll use the Supabase auth.users table directly for seeding

-- Insert admin user
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('admin123456', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "CloudHost Admin"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
) ON CONFLICT (email) DO NOTHING;

-- Insert regular user
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('user123456', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Test User"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
) ON CONFLICT (email) DO NOTHING;

-- Now create profiles for these users
-- Admin profile
INSERT INTO public.profiles (
  id,
  email,
  full_name,
  phone,
  role,
  balance,
  status,
  created_at,
  updated_at
) 
SELECT 
  au.id,
  '<EMAIL>',
  'CloudHost Admin',
  '+6281234567890',
  'admin',
  0.00,
  'active',
  NOW(),
  NOW()
FROM auth.users au 
WHERE au.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  phone = EXCLUDED.phone,
  role = EXCLUDED.role,
  balance = EXCLUDED.balance,
  status = EXCLUDED.status,
  updated_at = NOW();

-- Regular user profile
INSERT INTO public.profiles (
  id,
  email,
  full_name,
  phone,
  role,
  balance,
  status,
  created_at,
  updated_at
) 
SELECT 
  au.id,
  '<EMAIL>',
  'Test User',
  '+6281234567891',
  'user',
  100000.00,
  'active',
  NOW(),
  NOW()
FROM auth.users au 
WHERE au.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  phone = EXCLUDED.phone,
  role = EXCLUDED.role,
  balance = EXCLUDED.balance,
  status = EXCLUDED.status,
  updated_at = NOW();

-- Add some sample transactions for the test user
INSERT INTO public.transactions (
  id,
  user_id,
  type,
  amount,
  balance_before,
  balance_after,
  description,
  created_at
)
SELECT 
  gen_random_uuid(),
  au.id,
  'topup',
  100000.00,
  0.00,
  100000.00,
  'Initial balance for testing',
  NOW()
FROM auth.users au 
WHERE au.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- Add a sample notification for the test user
INSERT INTO public.notifications (
  id,
  user_id,
  type,
  title,
  message,
  data,
  read,
  created_at
)
SELECT 
  gen_random_uuid(),
  au.id,
  'success',
  'Selamat Datang!',
  'Akun Anda telah berhasil dibuat. Selamat menggunakan CloudHost ID!',
  '{}',
  false,
  NOW()
FROM auth.users au 
WHERE au.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- Add a sample notification for the admin user
INSERT INTO public.notifications (
  id,
  user_id,
  type,
  title,
  message,
  data,
  read,
  created_at
)
SELECT 
  gen_random_uuid(),
  au.id,
  'info',
  'Admin Access Granted',
  'You have been granted administrator access to CloudHost ID platform.',
  '{}',
  false,
  NOW()
FROM auth.users au 
WHERE au.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- Seed data for CloudHost ID SaaS Platform
-- This file creates sample data for development and testing

-- Note: For user creation, use the TestAuth page in the application
-- This seed file only contains non-auth related sample data

-- Insert sample servers
INSERT INTO public.servers (
  id,
  name,
  hostname,
  ip_address,
  region,
  docker_api_url,
  docker_api_key,
  cpu_cores,
  memory_gb,
  storage_gb,
  cpu_usage,
  memory_usage,
  storage_usage,
  status,
  last_ping,
  created_at,
  updated_at
) VALUES
(
  gen_random_uuid(),
  'Jakarta Server 1',
  'jakarta-1.cloudhost.id',
  '**************',
  'jakarta',
  'tcp://**************:2376',
  'sample_api_key_1',
  8,
  32,
  500,
  25.5,
  45.2,
  23.1,
  'active',
  NOW(),
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'Singapore Server 1',
  'singapore-1.cloudhost.id',
  '**************',
  'singapore',
  'tcp://**************:2376',
  'sample_api_key_2',
  16,
  64,
  1000,
  18.3,
  32.7,
  15.8,
  'active',
  NOW(),
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'Surabaya Server 1',
  'surabaya-1.cloudhost.id',
  '**************',
  'surabaya',
  'tcp://**************:2376',
  'sample_api_key_3',
  4,
  16,
  250,
  42.1,
  58.9,
  35.2,
  'active',
  NOW(),
  NOW(),
  NOW()
);

-- Insert sample service templates (if not already exists)
INSERT INTO public.service_templates (
  id,
  name,
  description,
  type,
  category,
  docker_image,
  cpu_cores,
  memory_mb,
  storage_gb,
  ports,
  environment_vars,
  volume_mounts,
  hourly_price,
  monthly_price,
  status,
  created_at,
  updated_at
) VALUES
(
  gen_random_uuid(),
  'MySQL 8.0',
  'MySQL 8.0 database server with optimized configuration for production use',
  'database',
  'mysql',
  'mysql:8.0',
  1.0,
  1024,
  10,
  '[{"internal": 3306, "name": "mysql"}]'::jsonb,
  '{"MYSQL_ROOT_PASSWORD": "auto_generated", "MYSQL_DATABASE": "app_db", "MYSQL_USER": "app_user", "MYSQL_PASSWORD": "auto_generated"}'::jsonb,
  '[{"source": "/var/lib/mysql", "target": "/var/lib/mysql", "type": "volume"}]'::jsonb,
  1500.00,
  900000.00,
  'active',
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'PostgreSQL 15',
  'PostgreSQL 15 database with advanced features and performance optimizations',
  'database',
  'postgresql',
  'postgres:15-alpine',
  1.0,
  1024,
  10,
  '[{"internal": 5432, "name": "postgres"}]'::jsonb,
  '{"POSTGRES_DB": "app_db", "POSTGRES_USER": "app_user", "POSTGRES_PASSWORD": "auto_generated"}'::jsonb,
  '[{"source": "/var/lib/postgresql/data", "target": "/var/lib/postgresql/data", "type": "volume"}]'::jsonb,
  1600.00,
  950000.00,
  'active',
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'Redis 7',
  'Redis 7 in-memory data structure store for caching and session management',
  'database',
  'redis',
  'redis:7-alpine',
  0.5,
  512,
  2,
  '[{"internal": 6379, "name": "redis"}]'::jsonb,
  '{"REDIS_PASSWORD": "auto_generated"}'::jsonb,
  '[{"source": "/data", "target": "/data", "type": "volume"}]'::jsonb,
  800.00,
  480000.00,
  'active',
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'Node.js App',
  'Node.js application runtime with PM2 process manager',
  'application',
  'nodejs',
  'node:18-alpine',
  1.0,
  1024,
  5,
  '[{"internal": 3000, "name": "http"}]'::jsonb,
  '{"NODE_ENV": "production", "PORT": "3000"}'::jsonb,
  '[{"source": "/app", "target": "/app", "type": "bind"}]'::jsonb,
  1200.00,
  720000.00,
  'active',
  NOW(),
  NOW()
),
(
  gen_random_uuid(),
  'WordPress',
  'WordPress CMS with Apache web server and PHP 8.1',
  'application',
  'wordpress',
  'wordpress:6.3-apache',
  1.0,
  1024,
  10,
  '[{"internal": 80, "name": "http"}]'::jsonb,
  '{"WORDPRESS_DB_HOST": "mysql", "WORDPRESS_DB_USER": "wordpress", "WORDPRESS_DB_PASSWORD": "auto_generated", "WORDPRESS_DB_NAME": "wordpress"}'::jsonb,
  '[{"source": "/var/www/html", "target": "/var/www/html", "type": "volume"}]'::jsonb,
  1800.00,
  1080000.00,
  'active',
  NOW(),
  NOW()
);

-- Insert sample system metrics
INSERT INTO public.system_metrics (
  id,
  timestamp,
  total_services,
  running_services,
  failed_services,
  avg_cpu_usage,
  avg_memory_usage,
  avg_storage_usage,
  total_requests,
  response_time,
  created_at
) VALUES
(
  gen_random_uuid(),
  NOW() - INTERVAL '1 hour',
  15,
  14,
  1,
  45.2,
  62.8,
  23.5,
  1250,
  120.5,
  NOW() - INTERVAL '1 hour'
),
(
  gen_random_uuid(),
  NOW() - INTERVAL '2 hours',
  14,
  13,
  1,
  42.1,
  58.3,
  23.1,
  1180,
  115.2,
  NOW() - INTERVAL '2 hours'
),
(
  gen_random_uuid(),
  NOW() - INTERVAL '3 hours',
  13,
  12,
  1,
  38.9,
  55.7,
  22.8,
  1095,
  108.7,
  NOW() - INTERVAL '3 hours'
);

-- Insert sample system alerts
INSERT INTO public.system_alerts (
  id,
  user_id,
  type,
  title,
  message,
  source,
  source_id,
  acknowledged,
  resolved,
  created_at,
  updated_at
) VALUES
(
  gen_random_uuid(),
  NULL,
  'warning',
  'High CPU Usage',
  'Server Jakarta-1 is experiencing high CPU usage (85%)',
  'server',
  (SELECT id FROM servers WHERE name = 'Jakarta Server 1' LIMIT 1),
  false,
  false,
  NOW() - INTERVAL '30 minutes',
  NOW() - INTERVAL '30 minutes'
),
(
  gen_random_uuid(),
  NULL,
  'info',
  'Maintenance Scheduled',
  'Scheduled maintenance for Singapore Server 1 on Sunday 2AM-4AM',
  'server',
  (SELECT id FROM servers WHERE name = 'Singapore Server 1' LIMIT 1),
  false,
  false,
  NOW() - INTERVAL '2 hours',
  NOW() - INTERVAL '2 hours'
);

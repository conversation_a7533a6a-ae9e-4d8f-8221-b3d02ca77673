import { corsHeaders } from '../_shared/cors.ts';

interface PaymentRequest {
  amount: number;
  payment_method: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  order_items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
}

const TRIPAY_MERCHANT_CODE = Deno.env.get('TRIPAY_MERCHANT_CODE')!;
const TRIPAY_API_KEY = Deno.env.get('TRIPAY_API_KEY')!;
const TRIPAY_PRIVATE_KEY = Deno.env.get('TRIPAY_PRIVATE_KEY')!;
const TRIPAY_BASE_URL = 'https://tripay.co.id/api-sandbox'; // Use production URL for live

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user } } = await supabaseClient.auth.getUser(token);

    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const paymentData: PaymentRequest = await req.json();

    // Generate merchant reference
    const merchantRef = `TOP-${user.id.slice(0, 8)}-${Date.now()}`;
    
    // Calculate signature
    const signature = await generateSignature(merchantRef, paymentData.amount);

    // Prepare Tripay request
    const tripayPayload = {
      method: paymentData.payment_method,
      merchant_ref: merchantRef,
      amount: paymentData.amount,
      customer_name: paymentData.customer_name,
      customer_email: paymentData.customer_email,
      customer_phone: paymentData.customer_phone,
      order_items: paymentData.order_items,
      return_url: `${req.headers.get('origin')}/payment/success`,
      expired_time: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      signature: signature
    };

    // Call Tripay API
    const tripayResponse = await fetch(`${TRIPAY_BASE_URL}/transaction/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TRIPAY_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tripayPayload)
    });

    const tripayResult = await tripayResponse.json();

    if (!tripayResult.success) {
      throw new Error(tripayResult.message || 'Failed to create payment');
    }

    const paymentInfo = tripayResult.data;

    // Store in database
    const { data: tripayTransaction, error: dbError } = await supabaseClient
      .from('tripay_transactions')
      .insert({
        user_id: user.id,
        reference: paymentInfo.reference,
        merchant_ref: merchantRef,
        payment_method: paymentData.payment_method,
        payment_name: paymentInfo.payment_name,
        amount: paymentData.amount,
        fee: paymentInfo.total_fee,
        total_amount: paymentInfo.amount_received,
        pay_code: paymentInfo.pay_code,
        pay_url: paymentInfo.pay_url,
        checkout_url: paymentInfo.checkout_url,
        expired_time: new Date(paymentInfo.expired_time * 1000).toISOString()
      })
      .select()
      .single();

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    return new Response(JSON.stringify({
      success: true,
      data: {
        reference: paymentInfo.reference,
        payment_method: paymentInfo.payment_method_name,
        amount: paymentInfo.amount_received,
        fee: paymentInfo.total_fee,
        pay_code: paymentInfo.pay_code,
        pay_url: paymentInfo.pay_url,
        checkout_url: paymentInfo.checkout_url,
        expired_time: paymentInfo.expired_time,
        instructions: paymentInfo.instructions
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Payment creation error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

async function generateSignature(merchantRef: string, amount: number): Promise<string> {
  const data = `${TRIPAY_MERCHANT_CODE}${merchantRef}${amount}`;
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(TRIPAY_PRIVATE_KEY),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(data));
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

// Import required
import { createClient } from 'npm:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface ProvisionRequest {
  service_template_id: string;
  service_name: string;
  server_id?: string;
}

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user } } = await supabaseClient.auth.getUser(token);

    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { service_template_id, service_name, server_id }: ProvisionRequest = await req.json();

    // Get service template
    const { data: template, error: templateError } = await supabaseClient
      .from('service_templates')
      .select('*')
      .eq('id', service_template_id)
      .single();

    if (templateError || !template) {
      throw new Error('Service template not found');
    }

    // Get user profile and check balance
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('balance')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      throw new Error('User profile not found');
    }

    // Check if user has enough balance for at least 1 hour
    if (Number(profile.balance) < Number(template.hourly_price)) {
      throw new Error('Saldo tidak mencukupi. Minimal saldo Rp ' + template.hourly_price.toLocaleString('id-ID'));
    }

    // Select server (auto-select if not specified)
    let selectedServerId = server_id;
    if (!selectedServerId) {
      const { data: servers } = await supabaseClient
        .from('servers')
        .select('id')
        .eq('status', 'active')
        .order('cpu_usage', { ascending: true })
        .limit(1);
      
      if (!servers || servers.length === 0) {
        throw new Error('No available servers');
      }
      selectedServerId = servers[0].id;
    }

    // Generate credentials
    const credentials = generateCredentials(template.category);
    
    // Generate container name
    const containerName = `${template.category}-${user.id.slice(0, 8)}-${Date.now()}`;
    
    // Create service instance
    const { data: instance, error: instanceError } = await supabaseClient
      .from('service_instances')
      .insert({
        user_id: user.id,
        service_template_id: service_template_id,
        server_id: selectedServerId,
        name: service_name,
        status: 'pending',
        container_name: containerName,
        credentials: credentials,
        environment_vars: {
          ...template.environment_vars,
          ...credentials
        }
      })
      .select()
      .single();

    if (instanceError) {
      throw new Error(`Failed to create service instance: ${instanceError.message}`);
    }

    // Start provisioning process (simulate with timeout)
    setTimeout(async () => {
      await provisionContainer(supabaseClient, instance.id, template, containerName, credentials);
    }, 1000);

    return new Response(JSON.stringify({
      success: true,
      data: {
        instance_id: instance.id,
        name: service_name,
        status: 'pending',
        message: 'Service provisioning started'
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Provisioning error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

function generateCredentials(category: string): any {
  const randomPassword = generateRandomString(16);
  const randomUser = generateRandomString(8);

  switch (category) {
    case 'postgresql':
      return {
        POSTGRES_USER: randomUser,
        POSTGRES_PASSWORD: randomPassword,
        POSTGRES_DB: 'mydb'
      };
    case 'mysql':
      return {
        MYSQL_USER: randomUser,
        MYSQL_PASSWORD: randomPassword,
        MYSQL_ROOT_PASSWORD: generateRandomString(20),
        MYSQL_DATABASE: 'mydb'
      };
    case 'mongodb':
      return {
        MONGO_INITDB_ROOT_USERNAME: 'admin',
        MONGO_INITDB_ROOT_PASSWORD: randomPassword,
        MONGO_INITDB_DATABASE: 'mydb'
      };
    case 'n8n':
      return {
        N8N_BASIC_AUTH_USER: 'admin',
        N8N_BASIC_AUTH_PASSWORD: randomPassword
      };
    case 'redis':
      return {
        REDIS_PASSWORD: randomPassword
      };
    default:
      return {};
  }
}

function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

async function provisionContainer(supabaseClient: any, instanceId: string, template: any, containerName: string, credentials: any) {
  try {
    // Update status to provisioning
    await supabaseClient
      .from('service_instances')
      .update({ status: 'provisioning' })
      .eq('id', instanceId);

    // Simulate Docker container creation
    // In production, this would call Docker API
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Generate random port (simulate)
    const externalPort = Math.floor(Math.random() * 10000) + 20000;
    const domain = `${containerName}.saas-platform.com`;

    // Update instance with container info
    await supabaseClient
      .from('service_instances')
      .update({
        status: 'running',
        container_id: `container_${Math.random().toString(36).substr(2, 9)}`,
        external_port: externalPort,
        domain: domain,
        connection_info: {
          host: domain,
          port: externalPort,
          ...credentials
        }
      })
      .eq('id', instanceId);

    // Get user ID for notification
    const { data: instance } = await supabaseClient
      .from('service_instances')
      .select('user_id, name')
      .eq('id', instanceId)
      .single();

    // Create success notification
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: instance.user_id,
        type: 'success',
        title: 'Service Berhasil Dibuat',
        message: `${instance.name} telah berhasil di-deploy dan siap digunakan`,
        data: { instance_id: instanceId }
      });

  } catch (error) {
    console.error('Container provisioning failed:', error);
    
    // Update status to failed
    await supabaseClient
      .from('service_instances')
      .update({ status: 'failed' })
      .eq('id', instanceId);

    // Get user ID for notification
    const { data: instance } = await supabaseClient
      .from('service_instances')
      .select('user_id, name')
      .eq('id', instanceId)
      .single();

    // Create error notification
    await supabaseClient
      .from('notifications')
      .insert({
        user_id: instance.user_id,
        type: 'error',
        title: 'Service Gagal Dibuat',
        message: `Terjadi kesalahan saat membuat ${instance.name}. Silakan coba lagi.`,
        data: { instance_id: instanceId, error: error.message }
      });
  }
}

// Import required
import { createClient } from 'npm:@supabase/supabase-js@2';
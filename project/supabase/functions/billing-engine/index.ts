import { corsHeaders } from '../_shared/cors.ts';

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get all running services
    const { data: runningServices, error: servicesError } = await supabaseClient
      .from('service_instances')
      .select(`
        id,
        user_id,
        name,
        last_billed,
        service_templates!inner(hourly_price)
      `)
      .eq('status', 'running');

    if (servicesError) {
      throw new Error(`Failed to get running services: ${servicesError.message}`);
    }

    const now = new Date();
    const billingResults = [];

    for (const service of runningServices) {
      const lastBilled = new Date(service.last_billed);
      const hoursSinceLastBilling = (now.getTime() - lastBilled.getTime()) / (1000 * 60 * 60);

      // Bill if more than 1 hour has passed
      if (hoursSinceLastBilling >= 1) {
        const hoursToBill = Math.floor(hoursSinceLastBilling);
        const totalCost = hoursToBill * Number(service.service_templates.hourly_price);

        // Get user current balance
        const { data: profile, error: profileError } = await supabaseClient
          .from('profiles')
          .select('balance')
          .eq('id', service.user_id)
          .single();

        if (profileError) {
          console.error(`Failed to get profile for user ${service.user_id}:`, profileError);
          continue;
        }

        const currentBalance = Number(profile.balance);

        if (currentBalance >= totalCost) {
          // Deduct from balance
          const newBalance = currentBalance - totalCost;

          const { error: balanceError } = await supabaseClient
            .from('profiles')
            .update({ balance: newBalance })
            .eq('id', service.user_id);

          if (balanceError) {
            console.error(`Failed to update balance for user ${service.user_id}:`, balanceError);
            continue;
          }

          // Create transaction record
          await supabaseClient
            .from('transactions')
            .insert({
              user_id: service.user_id,
              type: 'deduction',
              amount: -totalCost,
              balance_before: currentBalance,
              balance_after: newBalance,
              description: `Biaya layanan ${service.name} untuk ${hoursToBill} jam`,
              service_instance_id: service.id
            });

          // Update last_billed timestamp
          await supabaseClient
            .from('service_instances')
            .update({ last_billed: now.toISOString() })
            .eq('id', service.id);

          billingResults.push({
            service_id: service.id,
            service_name: service.name,
            user_id: service.user_id,
            hours_billed: hoursToBill,
            amount: totalCost,
            status: 'success'
          });

        } else {
          // Insufficient balance - suspend service
          await supabaseClient
            .from('service_instances')
            .update({ status: 'stopped' })
            .eq('id', service.id);

          // Create notification
          await supabaseClient
            .from('notifications')
            .insert({
              user_id: service.user_id,
              type: 'warning',
              title: 'Layanan Dihentikan',
              message: `${service.name} dihentikan karena saldo tidak mencukupi. Silakan top-up saldo Anda.`,
              data: { service_id: service.id, required_amount: totalCost }
            });

          billingResults.push({
            service_id: service.id,
            service_name: service.name,
            user_id: service.user_id,
            hours_billed: hoursToBill,
            amount: totalCost,
            status: 'insufficient_balance'
          });
        }
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Processed billing for ${billingResults.length} services`,
      results: billingResults
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Billing engine error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

// Import required
import { createClient } from 'npm:@supabase/supabase-js@2';
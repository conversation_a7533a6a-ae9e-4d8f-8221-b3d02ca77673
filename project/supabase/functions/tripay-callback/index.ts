import { corsHeaders } from '../_shared/cors.ts';

const TRIPAY_PRIVATE_KEY = Deno.env.get('TRIPAY_PRIVATE_KEY')!;

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const callbackData = await req.json();
    
    // Verify signature
    const signature = req.headers.get('X-Callback-Signature');
    const calculatedSignature = await generateCallbackSignature(JSON.stringify(callbackData));
    
    if (signature !== calculatedSignature) {
      return new Response(JSON.stringify({ error: 'Invalid signature' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { reference, status, paid_amount, paid_at } = callbackData;

    // Update tripay transaction
    const { data: tripayTransaction, error: updateError } = await supabaseClient
      .from('tripay_transactions')
      .update({
        status: status,
        paid_at: paid_at ? new Date(paid_at * 1000).toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('reference', reference)
      .select('user_id, amount')
      .single();

    if (updateError) {
      throw new Error(`Failed to update transaction: ${updateError.message}`);
    }

    // If payment is successful, update user balance
    if (status === 'PAID' && tripayTransaction) {
      // Get current user balance
      const { data: profile, error: profileError } = await supabaseClient
        .from('profiles')
        .select('balance')
        .eq('id', tripayTransaction.user_id)
        .single();

      if (profileError) {
        throw new Error(`Failed to get user profile: ${profileError.message}`);
      }

      const newBalance = Number(profile.balance) + Number(tripayTransaction.amount);

      // Update user balance
      const { error: balanceError } = await supabaseClient
        .from('profiles')
        .update({ balance: newBalance })
        .eq('id', tripayTransaction.user_id);

      if (balanceError) {
        throw new Error(`Failed to update balance: ${balanceError.message}`);
      }

      // Create transaction record
      const { error: transactionError } = await supabaseClient
        .from('transactions')
        .insert({
          user_id: tripayTransaction.user_id,
          type: 'topup',
          amount: tripayTransaction.amount,
          balance_before: profile.balance,
          balance_after: newBalance,
          description: `Top-up saldo via ${callbackData.payment_method_name}`,
          tripay_transaction_id: (await supabaseClient
            .from('tripay_transactions')
            .select('id')
            .eq('reference', reference)
            .single()).data?.id
        });

      if (transactionError) {
        throw new Error(`Failed to create transaction record: ${transactionError.message}`);
      }

      // Create notification
      await supabaseClient
        .from('notifications')
        .insert({
          user_id: tripayTransaction.user_id,
          type: 'success',
          title: 'Top-up Berhasil',
          message: `Saldo Anda telah bertambah Rp ${tripayTransaction.amount.toLocaleString('id-ID')}`,
          data: { transaction_reference: reference }
        });
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Callback processing error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

async function generateCallbackSignature(body: string): Promise<string> {
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(TRIPAY_PRIVATE_KEY),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(body));
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

// Import required
import { createClient } from 'npm:@supabase/supabase-js@2';
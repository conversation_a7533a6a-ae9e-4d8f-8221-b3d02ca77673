-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_alerts ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get current user profile
CREATE OR REPLACE FUNCTION get_user_profile()
RETURNS public.profiles AS $$
DECLARE
    profile public.profiles;
BEGIN
    SELECT * INTO profile FROM public.profiles WHERE id = auth.uid();
    RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can insert profiles" ON public.profiles
    FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admins can delete profiles" ON public.profiles
    FOR DELETE USING (is_admin());

-- Service templates policies
CREATE POLICY "Anyone can view active templates" ON public.service_templates
    FOR SELECT USING (status = 'active');

CREATE POLICY "Admins can manage all templates" ON public.service_templates
    FOR ALL USING (is_admin());

-- Servers policies
CREATE POLICY "Admins can manage all servers" ON public.servers
    FOR ALL USING (is_admin());

-- Service instances policies
CREATE POLICY "Users can view own services" ON public.service_instances
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own services" ON public.service_instances
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own services" ON public.service_instances
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own services" ON public.service_instances
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all services" ON public.service_instances
    FOR ALL USING (is_admin());

-- Service links policies
CREATE POLICY "Users can view own service links" ON public.service_links
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own service links" ON public.service_links
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all service links" ON public.service_links
    FOR ALL USING (is_admin());

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all transactions" ON public.transactions
    FOR ALL USING (is_admin());

-- Billing records policies
CREATE POLICY "Users can view own billing records" ON public.billing_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all billing records" ON public.billing_records
    FOR ALL USING (is_admin());

-- System metrics policies (admin only)
CREATE POLICY "Admins can manage system metrics" ON public.system_metrics
    FOR ALL USING (is_admin());

-- System alerts policies
CREATE POLICY "Users can view own alerts" ON public.system_alerts
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own alerts" ON public.system_alerts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all alerts" ON public.system_alerts
    FOR ALL USING (is_admin());

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update user balance
CREATE OR REPLACE FUNCTION public.update_user_balance(
    user_id UUID,
    amount DECIMAL(15,2),
    transaction_type transaction_type,
    description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_balance DECIMAL(15,2);
    new_balance DECIMAL(15,2);
BEGIN
    -- Get current balance
    SELECT balance INTO current_balance 
    FROM public.profiles 
    WHERE id = user_id;
    
    IF current_balance IS NULL THEN
        RAISE EXCEPTION 'User not found';
    END IF;
    
    -- Calculate new balance
    IF transaction_type IN ('topup', 'refund', 'adjustment') THEN
        new_balance := current_balance + amount;
    ELSE
        new_balance := current_balance - amount;
        
        -- Check if user has sufficient balance for deductions
        IF new_balance < 0 AND transaction_type = 'deduction' THEN
            RAISE EXCEPTION 'Insufficient balance';
        END IF;
    END IF;
    
    -- Update balance
    UPDATE public.profiles 
    SET balance = new_balance, updated_at = NOW()
    WHERE id = user_id;
    
    -- Create transaction record
    INSERT INTO public.transactions (user_id, type, amount, description, status)
    VALUES (user_id, transaction_type, amount, description, 'completed');
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to calculate service billing
CREATE OR REPLACE FUNCTION public.calculate_service_billing(
    service_id UUID,
    billing_end TIMESTAMPTZ DEFAULT NOW()
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    service_record public.service_instances;
    last_billing TIMESTAMPTZ;
    hours_used DECIMAL(10,2);
    total_cost DECIMAL(15,2);
BEGIN
    -- Get service details
    SELECT * INTO service_record 
    FROM public.service_instances 
    WHERE id = service_id;
    
    IF service_record IS NULL THEN
        RAISE EXCEPTION 'Service not found';
    END IF;
    
    -- Calculate hours since last billing
    last_billing := service_record.last_billed;
    hours_used := EXTRACT(EPOCH FROM (billing_end - last_billing)) / 3600.0;
    
    -- Calculate cost based on billing type
    IF service_record.billing_type = 'monthly' AND service_record.monthly_rate IS NOT NULL THEN
        total_cost := (service_record.monthly_rate / 720.0) * hours_used; -- 720 hours in a month
    ELSE
        total_cost := service_record.hourly_rate * hours_used;
    END IF;
    
    -- Create billing record
    INSERT INTO public.billing_records (
        user_id, 
        service_instance_id, 
        billing_period_start, 
        billing_period_end,
        usage_hours, 
        rate_per_hour, 
        total_amount
    ) VALUES (
        service_record.user_id,
        service_id,
        last_billing,
        billing_end,
        hours_used,
        service_record.hourly_rate,
        total_cost
    );
    
    -- Update last_billed timestamp
    UPDATE public.service_instances 
    SET last_billed = billing_end 
    WHERE id = service_id;
    
    RETURN total_cost;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to process service billing and deduct from balance
CREATE OR REPLACE FUNCTION public.process_service_billing(service_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    service_record public.service_instances;
    billing_amount DECIMAL(15,2);
BEGIN
    -- Get service details
    SELECT * INTO service_record 
    FROM public.service_instances 
    WHERE id = service_id;
    
    IF service_record IS NULL THEN
        RAISE EXCEPTION 'Service not found';
    END IF;
    
    -- Calculate billing amount
    billing_amount := public.calculate_service_billing(service_id);
    
    -- Deduct from user balance
    IF billing_amount > 0 THEN
        PERFORM public.update_user_balance(
            service_record.user_id,
            billing_amount,
            'deduction',
            'Service billing for ' || service_record.name
        );
    END IF;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        -- If billing fails (e.g., insufficient balance), suspend the service
        UPDATE public.service_instances 
        SET status = 'suspended' 
        WHERE id = service_id;
        
        -- Create alert
        INSERT INTO public.system_alerts (
            user_id, type, title, message, source, source_id
        ) VALUES (
            service_record.user_id,
            'critical',
            'Service Suspended',
            'Service ' || service_record.name || ' has been suspended due to insufficient balance',
            'billing_system',
            service_id
        );
        
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

/*
  # Seed Data for SaaS Platform

  1. Service Templates
    - Database services (PostgreSQL, MySQL, MongoDB)
    - Application services (n8n, WordPress)
    
  2. Sample Server
    - Default Docker host server
    
  3. Admin User Setup
    - Insert admin profile (will be created after first admin signup)
*/

-- Insert service templates
INSERT INTO service_templates (name, type, category, docker_image, cpu_cores, memory_mb, storage_gb, hourly_price, monthly_price, description, features, ports, environment_vars) VALUES
-- Database Services
('PostgreSQL 15', 'database', 'postgresql', 'postgres:15-alpine', 0.5, 512, 5, 150.00, 75000.00, 'PostgreSQL database server dengan performa tinggi', 
 '["ACID compliant", "Advanced indexing", "JSON support", "Extensions support"]',
 '[{"internal": 5432, "name": "postgresql"}]',
 '{"POSTGRES_DB": "mydb", "POSTGRES_USER": "user", "POSTGRES_PASSWORD": ""}'),

('MySQL 8.0', 'database', 'mysql', 'mysql:8.0', 0.5, 512, 5, 150.00, 75000.00, 'MySQL database server yang reliable dan cepat',
 '["InnoDB storage engine", "Full-text indexing", "JSON data type", "Replication support"]',
 '[{"internal": 3306, "name": "mysql"}]',
 '{"MYSQL_DATABASE": "mydb", "MYSQL_USER": "user", "MYSQL_PASSWORD": "", "MYSQL_ROOT_PASSWORD": ""}'),

('MongoDB 7.0', 'database', 'mongodb', 'mongo:7.0', 1.0, 1024, 10, 300.00, 150000.00, 'MongoDB NoSQL database untuk aplikasi modern',
 '["Document-oriented", "Horizontal scaling", "Aggregation framework", "GridFS for files"]',
 '[{"internal": 27017, "name": "mongodb"}]',
 '{"MONGO_INITDB_DATABASE": "mydb", "MONGO_INITDB_ROOT_USERNAME": "admin", "MONGO_INITDB_ROOT_PASSWORD": ""}'),

-- Application Services  
('n8n Workflow Automation', 'application', 'n8n', 'n8nio/n8n:latest', 1.0, 1024, 5, 200.00, 100000.00, 'Platform otomasi workflow yang powerful',
 '["Visual workflow editor", "400+ integrations", "Custom functions", "Webhook support"]',
 '[{"internal": 5678, "name": "web"}]',
 '{"N8N_BASIC_AUTH_ACTIVE": "true", "N8N_BASIC_AUTH_USER": "admin", "N8N_BASIC_AUTH_PASSWORD": ""}'),

('WordPress CMS', 'application', 'wordpress', 'wordpress:latest', 0.5, 512, 5, 100.00, 50000.00, 'Content Management System terpopuler di dunia',
 '["Easy content management", "Plugin ecosystem", "Theme customization", "SEO friendly"]',
 '[{"internal": 80, "name": "web"}]',
 '{"WORDPRESS_DB_HOST": "", "WORDPRESS_DB_NAME": "wordpress", "WORDPRESS_DB_USER": "wordpress", "WORDPRESS_DB_PASSWORD": ""}'),

('Redis Cache', 'database', 'redis', 'redis:7-alpine', 0.25, 256, 1, 75.00, 37500.00, 'In-memory database untuk caching dan session storage',
 '["High performance", "Data structures", "Pub/Sub messaging", "Lua scripting"]',
 '[{"internal": 6379, "name": "redis"}]',
 '{"REDIS_PASSWORD": ""}');

-- Insert default server
INSERT INTO servers (name, hostname, ip_address, region, docker_api_url, cpu_cores, memory_gb, storage_gb) VALUES
('Jakarta Server 1', 'docker-host-1.saas-platform.com', '*************', 'jakarta', 'https://docker-host-1.saas-platform.com:2376', 8, 16, 500);

-- Insert sample notifications (these will be system-wide)
-- Note: user_id will be filled when actual users register
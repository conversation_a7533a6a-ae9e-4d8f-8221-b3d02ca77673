/*
  # Complete SaaS Platform Database Schema

  1. New Tables
    - `profiles` - User profile information with Indonesian phone numbers
    - `tripay_transactions` - Tripay payment tracking with all payment methods
    - `transactions` - Internal transaction logs (topup, deduction, refund)
    - `service_templates` - Available services with pricing in IDR
    - `service_instances` - User's active services with container info
    - `servers` - Docker host servers management
    - `service_links` - Application to database connections
    - `notifications` - System notifications for users

  2. Security
    - Enable RLS on all tables
    - Add policies for multi-tenant isolation
    - Admin-only access for management tables

  3. Features
    - Real-time subscriptions for service status
    - Balance management with Tripay integration
    - Automated provisioning system
    - Multi-server deployment support
*/

-- Profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text NOT NULL,
  full_name text,
  phone text,
  role text NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin')),
  balance decimal(15,2) NOT NULL DEFAULT 0.00,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'banned')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Tripay transactions table
CREATE TABLE IF NOT EXISTS tripay_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  reference text UNIQUE NOT NULL,
  merchant_ref text UNIQUE NOT NULL,
  payment_method text NOT NULL,
  payment_name text NOT NULL,
  amount decimal(15,2) NOT NULL,
  fee decimal(15,2) NOT NULL DEFAULT 0.00,
  total_amount decimal(15,2) NOT NULL,
  status text NOT NULL DEFAULT 'UNPAID' CHECK (status IN ('UNPAID', 'PAID', 'EXPIRED', 'FAILED')),
  pay_code text,
  pay_url text,
  checkout_url text,
  expired_time timestamptz,
  paid_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Internal transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('topup', 'deduction', 'refund', 'adjustment')),
  amount decimal(15,2) NOT NULL,
  balance_before decimal(15,2) NOT NULL,
  balance_after decimal(15,2) NOT NULL,
  description text NOT NULL,
  tripay_transaction_id uuid REFERENCES tripay_transactions(id),
  service_instance_id uuid,
  created_at timestamptz DEFAULT now()
);

-- Service templates table
CREATE TABLE IF NOT EXISTS service_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  type text NOT NULL CHECK (type IN ('database', 'application')),
  category text NOT NULL, -- postgresql, mysql, mongodb, n8n, wordpress, etc.
  version text NOT NULL DEFAULT 'latest',
  docker_image text NOT NULL,
  docker_config jsonb NOT NULL DEFAULT '{}',
  cpu_cores decimal(3,1) NOT NULL DEFAULT 0.5,
  memory_mb integer NOT NULL DEFAULT 512,
  storage_gb integer NOT NULL DEFAULT 1,
  hourly_price decimal(10,2) NOT NULL,
  monthly_price decimal(10,2) NOT NULL,
  description text,
  features jsonb DEFAULT '[]',
  ports jsonb DEFAULT '[]',
  environment_vars jsonb DEFAULT '{}',
  volume_mounts jsonb DEFAULT '[]',
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Servers table
CREATE TABLE IF NOT EXISTS servers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  hostname text UNIQUE NOT NULL,
  ip_address text UNIQUE NOT NULL,
  region text NOT NULL DEFAULT 'jakarta',
  docker_api_url text NOT NULL,
  docker_api_key text,
  cpu_cores integer NOT NULL DEFAULT 4,
  memory_gb integer NOT NULL DEFAULT 8,
  storage_gb integer NOT NULL DEFAULT 100,
  cpu_usage decimal(5,2) DEFAULT 0.00,
  memory_usage decimal(5,2) DEFAULT 0.00,
  storage_usage decimal(5,2) DEFAULT 0.00,
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'offline', 'overloaded')),
  last_ping timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Service instances table
CREATE TABLE IF NOT EXISTS service_instances (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  service_template_id uuid NOT NULL REFERENCES service_templates(id),
  server_id uuid NOT NULL REFERENCES servers(id),
  name text NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'provisioning', 'running', 'stopped', 'failed', 'terminated')),
  container_id text,
  container_name text,
  internal_port integer,
  external_port integer,
  domain text,
  ssl_enabled boolean DEFAULT false,
  credentials jsonb DEFAULT '{}',
  connection_info jsonb DEFAULT '{}',
  environment_vars jsonb DEFAULT '{}',
  resource_limits jsonb DEFAULT '{}',
  last_billed timestamptz DEFAULT now(),
  expires_at timestamptz,
  terminated_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Service links table (for app-to-database connections)
CREATE TABLE IF NOT EXISTS service_links (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  application_id uuid NOT NULL REFERENCES service_instances(id) ON DELETE CASCADE,
  database_id uuid NOT NULL REFERENCES service_instances(id) ON DELETE CASCADE,
  connection_config jsonb NOT NULL DEFAULT '{}',
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(application_id, database_id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb DEFAULT '{}',
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tripay_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile"
  ON profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles"
  ON profiles FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for tripay_transactions
CREATE POLICY "Users can view own transactions"
  ON tripay_transactions FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can view all tripay transactions"
  ON tripay_transactions FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for transactions
CREATE POLICY "Users can view own transaction history"
  ON transactions FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can view all transactions"
  ON transactions FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for service_templates
CREATE POLICY "Anyone can view active service templates"
  ON service_templates FOR SELECT
  TO authenticated
  USING (status = 'active');

CREATE POLICY "Admins can manage service templates"
  ON service_templates FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for servers
CREATE POLICY "Admins can manage servers"
  ON servers FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for service_instances
CREATE POLICY "Users can view own services"
  ON service_instances FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update own services"
  ON service_instances FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all services"
  ON service_instances FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for service_links
CREATE POLICY "Users can manage own service links"
  ON service_links FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all service links"
  ON service_links FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications"
  ON notifications FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update own notifications"
  ON notifications FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all notifications"
  ON notifications FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Functions and triggers
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO profiles (id, email, full_name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tripay_transactions_updated_at BEFORE UPDATE ON tripay_transactions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_templates_updated_at BEFORE UPDATE ON service_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_servers_updated_at BEFORE UPDATE ON servers
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_instances_updated_at BEFORE UPDATE ON service_instances
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_links_updated_at BEFORE UPDATE ON service_links
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_tripay_transactions_user_id ON tripay_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_tripay_transactions_reference ON tripay_transactions(reference);
CREATE INDEX IF NOT EXISTS idx_tripay_transactions_status ON tripay_transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_service_instances_user_id ON service_instances(user_id);
CREATE INDEX IF NOT EXISTS idx_service_instances_status ON service_instances(status);
CREATE INDEX IF NOT EXISTS idx_service_instances_server_id ON service_instances(server_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
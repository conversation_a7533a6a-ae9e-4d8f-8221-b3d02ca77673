-- Insert default service templates

-- Database Templates
INSERT INTO public.service_templates (
    name, description, type, category, docker_image, 
    cpu_cores, memory_mb, storage_gb, 
    ports, environment_vars, volume_mounts,
    hourly_price, monthly_price
) VALUES 
-- PostgreSQL Templates
(
    'PostgreSQL 15',
    'PostgreSQL 15 database server with optimized configuration for production workloads',
    'database',
    'postgresql',
    'postgres:15-alpine',
    1.0,
    1024,
    5,
    '[{"internal": 5432, "name": "postgresql"}]'::jsonb,
    '{"POSTGRES_DB": "mydb", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/var/lib/postgresql/data", "target": "/var/lib/postgresql/data", "type": "volume"}]'::jsonb,
    2500.00,
    1500000.00
),
(
    'PostgreSQL 14',
    'PostgreSQL 14 database server with proven stability and performance',
    'database',
    'postgresql',
    'postgres:14-alpine',
    0.5,
    512,
    2,
    '[{"internal": 5432, "name": "postgresql"}]'::jsonb,
    '{"POSTGRES_DB": "mydb", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/var/lib/postgresql/data", "target": "/var/lib/postgresql/data", "type": "volume"}]'::jsonb,
    2000.00,
    1200000.00
),

-- MySQL Templates
(
    'MySQL 8.0',
    'MySQL 8.0 database server with enhanced performance and security features',
    'database',
    'mysql',
    'mysql:8.0',
    1.0,
    1024,
    5,
    '[{"internal": 3306, "name": "mysql"}]'::jsonb,
    '{"MYSQL_DATABASE": "mydb", "MYSQL_USER": "mysql", "MYSQL_PASSWORD": "auto_generated", "MYSQL_ROOT_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/var/lib/mysql", "target": "/var/lib/mysql", "type": "volume"}]'::jsonb,
    2300.00,
    1400000.00
),
(
    'MySQL 5.7',
    'MySQL 5.7 database server for legacy applications requiring older MySQL version',
    'database',
    'mysql',
    'mysql:5.7',
    0.5,
    512,
    2,
    '[{"internal": 3306, "name": "mysql"}]'::jsonb,
    '{"MYSQL_DATABASE": "mydb", "MYSQL_USER": "mysql", "MYSQL_PASSWORD": "auto_generated", "MYSQL_ROOT_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/var/lib/mysql", "target": "/var/lib/mysql", "type": "volume"}]'::jsonb,
    1800.00,
    1100000.00
),

-- MongoDB Templates
(
    'MongoDB 6.0',
    'MongoDB 6.0 NoSQL database with advanced querying and indexing capabilities',
    'database',
    'mongodb',
    'mongo:6.0',
    1.0,
    1024,
    5,
    '[{"internal": 27017, "name": "mongodb"}]'::jsonb,
    '{"MONGO_INITDB_DATABASE": "mydb", "MONGO_INITDB_ROOT_USERNAME": "admin", "MONGO_INITDB_ROOT_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/data/db", "target": "/data/db", "type": "volume"}]'::jsonb,
    2800.00,
    1700000.00
),

-- Redis Templates
(
    'Redis 7.0',
    'Redis 7.0 in-memory data structure store for caching and session management',
    'database',
    'redis',
    'redis:7.0-alpine',
    0.5,
    512,
    1,
    '[{"internal": 6379, "name": "redis"}]'::jsonb,
    '{"REDIS_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/data", "target": "/data", "type": "volume"}]'::jsonb,
    1500.00,
    900000.00
),

-- Application Templates
(
    'WordPress',
    'WordPress CMS with PHP 8.1 and Apache web server for blogs and websites',
    'application',
    'wordpress',
    'wordpress:6.3-apache',
    1.0,
    1024,
    3,
    '[{"internal": 80, "name": "http"}]'::jsonb,
    '{"WORDPRESS_DB_HOST": "database_host", "WORDPRESS_DB_NAME": "wordpress", "WORDPRESS_DB_USER": "wordpress", "WORDPRESS_DB_PASSWORD": "auto_generated"}'::jsonb,
    '[{"source": "/var/www/html", "target": "/var/www/html", "type": "volume"}]'::jsonb,
    3000.00,
    1800000.00
),

(
    'n8n Workflow Automation',
    'n8n workflow automation tool for connecting apps and automating tasks',
    'application',
    'n8n',
    'n8nio/n8n:latest',
    1.0,
    1024,
    2,
    '[{"internal": 5678, "name": "http"}]'::jsonb,
    '{"N8N_BASIC_AUTH_ACTIVE": "true", "N8N_BASIC_AUTH_USER": "admin", "N8N_BASIC_AUTH_PASSWORD": "auto_generated", "WEBHOOK_URL": "https://your-domain.com"}'::jsonb,
    '[{"source": "/home/<USER>/.n8n", "target": "/home/<USER>/.n8n", "type": "volume"}]'::jsonb,
    3500.00,
    2100000.00
),

(
    'Nextcloud',
    'Nextcloud file sharing and collaboration platform with web interface',
    'application',
    'nextcloud',
    'nextcloud:27-apache',
    2.0,
    2048,
    10,
    '[{"internal": 80, "name": "http"}]'::jsonb,
    '{"NEXTCLOUD_ADMIN_USER": "admin", "NEXTCLOUD_ADMIN_PASSWORD": "auto_generated", "NEXTCLOUD_TRUSTED_DOMAINS": "your-domain.com"}'::jsonb,
    '[{"source": "/var/www/html", "target": "/var/www/html", "type": "volume"}]'::jsonb,
    4000.00,
    2400000.00
),

(
    'Grafana',
    'Grafana analytics and monitoring platform with beautiful dashboards',
    'application',
    'grafana',
    'grafana/grafana:latest',
    0.5,
    512,
    2,
    '[{"internal": 3000, "name": "http"}]'::jsonb,
    '{"GF_SECURITY_ADMIN_USER": "admin", "GF_SECURITY_ADMIN_PASSWORD": "auto_generated", "GF_INSTALL_PLUGINS": "grafana-clock-panel,grafana-simple-json-datasource"}'::jsonb,
    '[{"source": "/var/lib/grafana", "target": "/var/lib/grafana", "type": "volume"}]'::jsonb,
    2200.00,
    1300000.00
),

(
    'Nginx',
    'Nginx web server for serving static content and reverse proxy',
    'application',
    'nginx',
    'nginx:alpine',
    0.5,
    256,
    1,
    '[{"internal": 80, "name": "http"}]'::jsonb,
    '{}'::jsonb,
    '[{"source": "/usr/share/nginx/html", "target": "/usr/share/nginx/html", "type": "volume"}, {"source": "/etc/nginx/conf.d", "target": "/etc/nginx/conf.d", "type": "volume"}]'::jsonb,
    1200.00,
    720000.00
),

(
    'Node.js Application',
    'Node.js runtime environment for running JavaScript applications',
    'application',
    'nodejs',
    'node:18-alpine',
    1.0,
    1024,
    2,
    '[{"internal": 3000, "name": "http"}]'::jsonb,
    '{"NODE_ENV": "production", "PORT": "3000"}'::jsonb,
    '[{"source": "/app", "target": "/app", "type": "volume"}]'::jsonb,
    2500.00,
    1500000.00
),

(
    'Python Application',
    'Python 3.11 runtime environment for running Python applications',
    'application',
    'python',
    'python:3.11-slim',
    1.0,
    1024,
    2,
    '[{"internal": 8000, "name": "http"}]'::jsonb,
    '{"PYTHONPATH": "/app", "PORT": "8000"}'::jsonb,
    '[{"source": "/app", "target": "/app", "type": "volume"}]'::jsonb,
    2500.00,
    1500000.00
);

-- Insert default servers (example servers)
INSERT INTO public.servers (
    name, hostname, ip_address, region, docker_api_url,
    cpu_cores, memory_gb, storage_gb, status
) VALUES 
(
    'Jakarta Server 1',
    'docker-jkt-1.cloudhost.id',
    '***************',
    'jakarta',
    'https://docker-jkt-1.cloudhost.id:2376',
    8,
    32,
    500,
    'active'
),
(
    'Singapore Server 1',
    'docker-sgp-1.cloudhost.id',
    '***************',
    'singapore',
    'https://docker-sgp-1.cloudhost.id:2376',
    16,
    64,
    1000,
    'active'
),
(
    'Sydney Server 1',
    'docker-syd-1.cloudhost.id',
    '**************',
    'sydney',
    'https://docker-syd-1.cloudhost.id:2376',
    8,
    32,
    500,
    'active'
);

-- Create admin user (this will be handled by the application)
-- The admin user should be created through the Supabase Auth UI or API

-- Insert some sample system metrics
INSERT INTO public.system_metrics (
    timestamp, total_services, running_services, failed_services,
    avg_cpu_usage, avg_memory_usage, avg_storage_usage,
    total_requests, response_time
) VALUES 
(NOW() - INTERVAL '1 hour', 15, 14, 1, 45.2, 62.8, 23.5, 1250, 120.5),
(NOW() - INTERVAL '2 hours', 14, 13, 1, 42.1, 58.3, 23.1, 1180, 115.2),
(NOW() - INTERVAL '3 hours', 13, 12, 1, 38.9, 55.7, 22.8, 1095, 108.7),
(NOW() - INTERVAL '4 hours', 12, 11, 1, 35.6, 52.1, 22.4, 980, 102.3),
(NOW() - INTERVAL '5 hours', 11, 10, 1, 32.4, 48.9, 22.0, 875, 98.1);

-- Create some sample alerts
INSERT INTO public.system_alerts (
    type, title, message, source, acknowledged, resolved
) VALUES 
(
    'info',
    'System Maintenance Scheduled',
    'Scheduled maintenance window for server updates on Sunday 2AM-4AM UTC',
    'maintenance_scheduler',
    true,
    false
),
(
    'warning',
    'High Memory Usage',
    'Server memory usage has exceeded 80% threshold',
    'resource_monitor',
    false,
    false
),
(
    'info',
    'Backup Completed',
    'Daily backup process completed successfully for all databases',
    'backup_service',
    true,
    true
);

-- CloudHost ID Database Setup
-- Run this script in Supabase SQL Editor

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('user', 'admin');
CREATE TYPE service_status AS ENUM ('pending', 'running', 'stopped', 'failed', 'suspended');
CREATE TYPE template_status AS ENUM ('active', 'inactive', 'deprecated');
CREATE TYPE template_type AS ENUM ('database', 'application');
CREATE TYPE server_status AS ENUM ('active', 'maintenance', 'offline', 'overloaded');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'cancelled');
CREATE TYPE transaction_type AS ENUM ('topup', 'deduction', 'refund', 'adjustment');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone TEXT,
    balance DECIMAL(15,2) DEFAULT 0.00,
    role user_role DEFAULT 'user',
    status TEXT DEFAULT 'active',
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service templates table
CREATE TABLE public.service_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    type template_type NOT NULL,
    category TEXT NOT NULL,
    version TEXT DEFAULT 'latest',
    docker_image TEXT NOT NULL,
    docker_config JSONB DEFAULT '{}',
    cpu_cores DECIMAL(3,1) NOT NULL DEFAULT 0.5,
    memory_mb INTEGER NOT NULL DEFAULT 512,
    storage_gb INTEGER NOT NULL DEFAULT 1,
    ports JSONB DEFAULT '[]',
    environment_vars JSONB DEFAULT '{}',
    volume_mounts JSONB DEFAULT '[]',
    features TEXT[] DEFAULT '{}',
    hourly_price DECIMAL(10,2) NOT NULL,
    monthly_price DECIMAL(10,2) NOT NULL,
    status template_status DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Servers table (Docker hosts)
CREATE TABLE public.servers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    hostname TEXT NOT NULL,
    ip_address INET NOT NULL,
    region TEXT NOT NULL,
    docker_api_url TEXT NOT NULL,
    docker_api_key TEXT,
    cpu_cores INTEGER NOT NULL,
    memory_gb INTEGER NOT NULL,
    storage_gb INTEGER NOT NULL,
    cpu_usage DECIMAL(5,2) DEFAULT 0.00,
    memory_usage DECIMAL(5,2) DEFAULT 0.00,
    storage_usage DECIMAL(5,2) DEFAULT 0.00,
    status server_status DEFAULT 'active',
    last_ping TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service instances table
CREATE TABLE public.service_instances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    service_template_id UUID REFERENCES public.service_templates(id) ON DELETE RESTRICT NOT NULL,
    server_id UUID REFERENCES public.servers(id) ON DELETE RESTRICT NOT NULL,
    name TEXT NOT NULL,
    docker_container_id TEXT,
    status service_status DEFAULT 'pending',
    cpu_cores DECIMAL(3,1) NOT NULL,
    memory_mb INTEGER NOT NULL,
    storage_gb INTEGER NOT NULL,
    ports JSONB DEFAULT '[]',
    environment_vars JSONB DEFAULT '{}',
    volume_mounts JSONB DEFAULT '[]',
    connection_info JSONB DEFAULT '{}',
    credentials JSONB DEFAULT '{}',
    billing_type TEXT DEFAULT 'hourly',
    hourly_rate DECIMAL(10,2) NOT NULL,
    monthly_rate DECIMAL(10,2),
    last_billed TIMESTAMPTZ DEFAULT NOW(),
    uptime_seconds INTEGER DEFAULT 0,
    last_health_check TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    terminated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- Service links table (for connecting applications to databases)
CREATE TABLE public.service_links (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    application_id UUID REFERENCES public.service_instances(id) ON DELETE CASCADE NOT NULL,
    database_id UUID REFERENCES public.service_instances(id) ON DELETE CASCADE NOT NULL,
    connection_config JSONB NOT NULL,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(application_id, database_id)
);

-- Transactions table
CREATE TABLE public.transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type transaction_type NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    tripay_transaction_id UUID,
    service_instance_id UUID REFERENCES public.service_instances(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tripay transactions table
CREATE TABLE public.tripay_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    reference TEXT UNIQUE NOT NULL,
    merchant_ref TEXT UNIQUE NOT NULL,
    payment_method TEXT NOT NULL,
    payment_name TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    status TEXT DEFAULT 'UNPAID',
    pay_code TEXT,
    pay_url TEXT,
    checkout_url TEXT,
    expired_time TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System metrics table
CREATE TABLE public.system_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    total_services INTEGER DEFAULT 0,
    running_services INTEGER DEFAULT 0,
    failed_services INTEGER DEFAULT 0,
    avg_cpu_usage DECIMAL(5,2) DEFAULT 0.00,
    avg_memory_usage DECIMAL(5,2) DEFAULT 0.00,
    avg_storage_usage DECIMAL(5,2) DEFAULT 0.00,
    total_requests INTEGER DEFAULT 0,
    response_time DECIMAL(8,2) DEFAULT 0.00,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System alerts table
CREATE TABLE public.system_alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    source TEXT NOT NULL,
    source_id UUID,
    acknowledged BOOLEAN DEFAULT false,
    resolved BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_service_templates_type ON public.service_templates(type);
CREATE INDEX idx_service_templates_status ON public.service_templates(status);
CREATE INDEX idx_servers_status ON public.servers(status);
CREATE INDEX idx_service_instances_user_id ON public.service_instances(user_id);
CREATE INDEX idx_service_instances_status ON public.service_instances(status);
CREATE INDEX idx_service_instances_server_id ON public.service_instances(server_id);
CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX idx_tripay_transactions_user_id ON public.tripay_transactions(user_id);
CREATE INDEX idx_tripay_transactions_reference ON public.tripay_transactions(reference);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_system_metrics_timestamp ON public.system_metrics(timestamp);
CREATE INDEX idx_system_alerts_user_id ON public.system_alerts(user_id);
CREATE INDEX idx_system_alerts_type ON public.system_alerts(type);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_templates_updated_at BEFORE UPDATE ON public.service_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_servers_updated_at BEFORE UPDATE ON public.servers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_instances_updated_at BEFORE UPDATE ON public.service_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_links_updated_at BEFORE UPDATE ON public.service_links FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tripay_transactions_updated_at BEFORE UPDATE ON public.tripay_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_alerts_updated_at BEFORE UPDATE ON public.system_alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

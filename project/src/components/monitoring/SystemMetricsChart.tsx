import React, { useState } from 'react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON>
} from 'recharts';
import { Activity, Cpu, MemoryStick, HardDrive, Zap } from 'lucide-react';

interface SystemMetrics {
  timestamp: string;
  total_services: number;
  running_services: number;
  failed_services: number;
  avg_cpu_usage: number;
  avg_memory_usage: number;
  avg_storage_usage: number;
  total_requests: number;
  response_time: number;
}

interface SystemMetricsChartProps {
  data: SystemMetrics[];
}

export function SystemMetricsChart({ data }: SystemMetricsChartProps) {
  const [activeMetric, setActiveMetric] = useState<'resources' | 'services' | 'performance'>('resources');

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name.includes('usage') || name.includes('cpu') || name.includes('memory') || name.includes('storage')) {
      return [`${value.toFixed(1)}%`, name];
    }
    if (name.includes('response_time')) {
      return [`${value.toFixed(0)}ms`, name];
    }
    return [value.toString(), name];
  };

  const metricTabs = [
    {
      id: 'resources',
      name: 'Resource Usage',
      icon: Cpu,
      color: 'blue'
    },
    {
      id: 'services',
      name: 'Service Status',
      icon: Activity,
      color: 'green'
    },
    {
      id: 'performance',
      name: 'Performance',
      icon: Zap,
      color: 'purple'
    }
  ];

  const renderResourceChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={formatTimestamp}
          className="text-xs"
        />
        <YAxis 
          domain={[0, 100]}
          className="text-xs"
        />
        <Tooltip 
          formatter={formatTooltipValue}
          labelFormatter={(label) => `Time: ${formatTimestamp(label)}`}
          contentStyle={{
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            border: 'none',
            borderRadius: '8px',
            color: 'white'
          }}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="avg_cpu_usage" 
          stroke="#3B82F6" 
          strokeWidth={2}
          name="CPU Usage"
          dot={false}
        />
        <Line 
          type="monotone" 
          dataKey="avg_memory_usage" 
          stroke="#10B981" 
          strokeWidth={2}
          name="Memory Usage"
          dot={false}
        />
        <Line 
          type="monotone" 
          dataKey="avg_storage_usage" 
          stroke="#F59E0B" 
          strokeWidth={2}
          name="Storage Usage"
          dot={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderServiceChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={formatTimestamp}
          className="text-xs"
        />
        <YAxis className="text-xs" />
        <Tooltip 
          formatter={formatTooltipValue}
          labelFormatter={(label) => `Time: ${formatTimestamp(label)}`}
          contentStyle={{
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            border: 'none',
            borderRadius: '8px',
            color: 'white'
          }}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="total_services" 
          stroke="#6366F1" 
          strokeWidth={2}
          name="Total Services"
          dot={false}
        />
        <Line 
          type="monotone" 
          dataKey="running_services" 
          stroke="#10B981" 
          strokeWidth={2}
          name="Running Services"
          dot={false}
        />
        <Line 
          type="monotone" 
          dataKey="failed_services" 
          stroke="#EF4444" 
          strokeWidth={2}
          name="Failed Services"
          dot={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderPerformanceChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={formatTimestamp}
          className="text-xs"
        />
        <YAxis 
          yAxisId="requests"
          orientation="left"
          className="text-xs"
        />
        <YAxis 
          yAxisId="response"
          orientation="right"
          className="text-xs"
        />
        <Tooltip 
          formatter={formatTooltipValue}
          labelFormatter={(label) => `Time: ${formatTimestamp(label)}`}
          contentStyle={{
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            border: 'none',
            borderRadius: '8px',
            color: 'white'
          }}
        />
        <Legend />
        <Line 
          yAxisId="requests"
          type="monotone" 
          dataKey="total_requests" 
          stroke="#8B5CF6" 
          strokeWidth={2}
          name="Total Requests"
          dot={false}
        />
        <Line 
          yAxisId="response"
          type="monotone" 
          dataKey="response_time" 
          stroke="#F59E0B" 
          strokeWidth={2}
          name="Response Time (ms)"
          dot={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderChart = () => {
    switch (activeMetric) {
      case 'resources':
        return renderResourceChart();
      case 'services':
        return renderServiceChart();
      case 'performance':
        return renderPerformanceChart();
      default:
        return renderResourceChart();
    }
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No metrics data available</p>
          <p className="text-sm">Metrics will appear once services are running</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Metric Tabs */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
        {metricTabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveMetric(tab.id as any)}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeMetric === tab.id
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <Icon className="w-4 h-4 mr-2" />
              {tab.name}
            </button>
          );
        })}
      </div>

      {/* Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
        {renderChart()}
      </div>

      {/* Current Values */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {activeMetric === 'resources' && (
          <>
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <Cpu className="w-4 h-4 text-blue-600 mr-1" />
                <span className="text-sm text-gray-600 dark:text-gray-400">CPU</span>
              </div>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.avg_cpu_usage?.toFixed(1) || 0}%
              </p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <MemoryStick className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Memory</span>
              </div>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.avg_memory_usage?.toFixed(1) || 0}%
              </p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <HardDrive className="w-4 h-4 text-yellow-600 mr-1" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Storage</span>
              </div>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.avg_storage_usage?.toFixed(1) || 0}%
              </p>
            </div>
          </>
        )}
        
        {activeMetric === 'services' && (
          <>
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total</span>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.total_services || 0}
              </p>
            </div>
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Running</span>
              <p className="text-lg font-semibold text-green-600">
                {data[data.length - 1]?.running_services || 0}
              </p>
            </div>
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Failed</span>
              <p className="text-lg font-semibold text-red-600">
                {data[data.length - 1]?.failed_services || 0}
              </p>
            </div>
          </>
        )}
        
        {activeMetric === 'performance' && (
          <>
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Requests</span>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.total_requests || 0}
              </p>
            </div>
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Response Time</span>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {data[data.length - 1]?.response_time?.toFixed(0) || 0}ms
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

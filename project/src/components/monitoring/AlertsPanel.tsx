import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle,
  X,
  Clock,
  Server,
  Database,
  Cpu,
  MemoryStick,
  HardDrive,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  source: string;
  source_id?: string;
  created_at: string;
  acknowledged: boolean;
  resolved: boolean;
}

export function AlertsPanel() {
  const { profile } = useAuth();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unresolved' | 'critical'>('unresolved');

  useEffect(() => {
    fetchAlerts();
    
    // Set up real-time subscription for alerts
    const subscription = supabase
      .channel('alerts')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'system_alerts',
          filter: `user_id=eq.${profile?.id}`
        }, 
        () => {
          fetchAlerts();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [profile?.id]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would fetch from system_alerts table
      // For now, we'll generate some mock alerts based on system state
      const mockAlerts: Alert[] = [
        {
          id: '1',
          type: 'critical',
          title: 'Service Down',
          message: 'PostgreSQL database service is not responding',
          source: 'service_monitor',
          source_id: 'postgres-prod-1',
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          acknowledged: false,
          resolved: false
        },
        {
          id: '2',
          type: 'warning',
          title: 'High CPU Usage',
          message: 'Server CPU usage is above 85% for the last 10 minutes',
          source: 'resource_monitor',
          source_id: 'server-1',
          created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          acknowledged: true,
          resolved: false
        },
        {
          id: '3',
          type: 'warning',
          title: 'Memory Usage High',
          message: 'Application server memory usage is at 78%',
          source: 'resource_monitor',
          source_id: 'app-server-2',
          created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          acknowledged: false,
          resolved: false
        },
        {
          id: '4',
          type: 'info',
          title: 'Backup Completed',
          message: 'Daily backup for all databases completed successfully',
          source: 'backup_service',
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          acknowledged: true,
          resolved: true
        },
        {
          id: '5',
          type: 'critical',
          title: 'Disk Space Low',
          message: 'Storage usage is at 92% on primary server',
          source: 'storage_monitor',
          source_id: 'server-1',
          created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          acknowledged: false,
          resolved: false
        }
      ];

      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Error fetching alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      // In a real implementation, this would update the database
      setAlerts(alerts.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      ));
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      // In a real implementation, this would update the database
      setAlerts(alerts.map(alert => 
        alert.id === alertId 
          ? { ...alert, resolved: true, acknowledged: true }
          : alert
      ));
    } catch (error) {
      console.error('Error resolving alert:', error);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return AlertTriangle;
      case 'warning': return AlertCircle;
      case 'info': return Info;
      default: return AlertCircle;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical': return 'text-red-600 dark:text-red-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'info': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getAlertBgColor = (type: string) => {
    switch (type) {
      case 'critical': return 'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700';
      case 'warning': return 'bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700';
      case 'info': return 'bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700';
      default: return 'bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'service_monitor': return Database;
      case 'resource_monitor': return Cpu;
      case 'storage_monitor': return HardDrive;
      case 'backup_service': return CheckCircle;
      default: return Server;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const filteredAlerts = alerts.filter(alert => {
    switch (filter) {
      case 'unresolved':
        return !alert.resolved;
      case 'critical':
        return alert.type === 'critical' && !alert.resolved;
      default:
        return true;
    }
  });

  const alertCounts = {
    all: alerts.length,
    unresolved: alerts.filter(a => !a.resolved).length,
    critical: alerts.filter(a => a.type === 'critical' && !a.resolved).length
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            System Alerts
          </h3>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="text-sm px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All ({alertCounts.all})</option>
            <option value="unresolved">Unresolved ({alertCounts.unresolved})</option>
            <option value="critical">Critical ({alertCounts.critical})</option>
          </select>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredAlerts.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              All Clear!
            </h4>
            <p className="text-gray-500 dark:text-gray-400">
              {filter === 'all' 
                ? 'No alerts in the system.' 
                : `No ${filter} alerts found.`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            <AnimatePresence>
              {filteredAlerts.map((alert, index) => {
                const AlertIcon = getAlertIcon(alert.type);
                const SourceIcon = getSourceIcon(alert.source);
                
                return (
                  <motion.div
                    key={alert.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ delay: index * 0.05 }}
                    className={`border rounded-lg p-4 ${getAlertBgColor(alert.type)} ${
                      alert.resolved ? 'opacity-60' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <AlertIcon className={`w-5 h-5 mt-0.5 ${getAlertColor(alert.type)}`} />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                              {alert.title}
                            </h4>
                            {alert.acknowledged && !alert.resolved && (
                              <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full">
                                Acknowledged
                              </span>
                            )}
                            {alert.resolved && (
                              <span className="px-2 py-0.5 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full">
                                Resolved
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                            {alert.message}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex items-center">
                              <SourceIcon className="w-3 h-3 mr-1" />
                              <span>{alert.source.replace('_', ' ')}</span>
                            </div>
                            <div className="flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              <span>{formatTimeAgo(alert.created_at)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {!alert.resolved && (
                        <div className="flex items-center space-x-1">
                          {!alert.acknowledged && (
                            <button
                              onClick={() => acknowledgeAlert(alert.id)}
                              className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                              title="Acknowledge"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => resolveAlert(alert.id)}
                            className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                            title="Resolve"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

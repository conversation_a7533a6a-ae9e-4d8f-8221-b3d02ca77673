import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Database, 
  Server, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock,
  Cpu,
  MemoryStick,
  HardDrive,
  Activity,
  Eye,
  RotateCcw,
  Trash2
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '../ui/Card';

interface ServiceInstance {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'failed' | 'pending';
  service_templates: {
    name: string;
    type: 'database' | 'application';
    category: string;
  };
  cpu_usage?: number;
  memory_usage?: number;
  storage_usage?: number;
  uptime?: number;
  last_health_check?: string;
  created_at: string;
}

interface ServiceHealthGridProps {
  services: ServiceInstance[];
}

export function ServiceHealthGrid({ services }: ServiceHealthGridProps) {
  const [filter, setFilter] = useState<'all' | 'running' | 'stopped' | 'failed'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'status' | 'uptime' | 'created'>('name');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600 dark:text-green-400';
      case 'stopped': return 'text-gray-600 dark:text-gray-400';
      case 'failed': return 'text-red-600 dark:text-red-400';
      case 'pending': return 'text-yellow-600 dark:text-yellow-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return CheckCircle;
      case 'stopped': return XCircle;
      case 'failed': return XCircle;
      case 'pending': return Clock;
      default: return AlertTriangle;
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      running: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      stopped: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[status as keyof typeof styles]}`}>
        {status}
      </span>
    );
  };

  const formatUptime = (uptime?: number) => {
    if (!uptime) return 'N/A';
    
    const days = Math.floor(uptime / (24 * 60 * 60));
    const hours = Math.floor((uptime % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((uptime % (60 * 60)) / 60);
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const formatLastHealthCheck = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const check = new Date(timestamp);
    const diffMs = now.getTime() - check.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getResourceUsageColor = (usage?: number) => {
    if (!usage) return 'text-gray-400';
    if (usage >= 90) return 'text-red-600 dark:text-red-400';
    if (usage >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const filteredServices = services.filter(service => {
    if (filter === 'all') return true;
    return service.status === filter;
  });

  const sortedServices = [...filteredServices].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'status':
        return a.status.localeCompare(b.status);
      case 'uptime':
        return (b.uptime || 0) - (a.uptime || 0);
      case 'created':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      default:
        return 0;
    }
  });

  const statusCounts = {
    all: services.length,
    running: services.filter(s => s.status === 'running').length,
    stopped: services.filter(s => s.status === 'stopped').length,
    failed: services.filter(s => s.status === 'failed').length
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Service Health Overview
          </h3>
          <div className="flex items-center space-x-4">
            {/* Filter */}
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="text-sm px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All ({statusCounts.all})</option>
              <option value="running">Running ({statusCounts.running})</option>
              <option value="stopped">Stopped ({statusCounts.stopped})</option>
              <option value="failed">Failed ({statusCounts.failed})</option>
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="name">Sort by Name</option>
              <option value="status">Sort by Status</option>
              <option value="uptime">Sort by Uptime</option>
              <option value="created">Sort by Created</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {sortedServices.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Services Found
            </h4>
            <p className="text-gray-500 dark:text-gray-400">
              {filter === 'all' 
                ? 'No services have been created yet.' 
                : `No services with status "${filter}" found.`
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedServices.map((service, index) => {
              const StatusIcon = getStatusIcon(service.status);
              const isDatabase = service.service_templates.type === 'database';
              
              return (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  {/* Service Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      {isDatabase ? (
                        <Database className="w-5 h-5 text-blue-600 mr-2" />
                      ) : (
                        <Server className="w-5 h-5 text-purple-600 mr-2" />
                      )}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                          {service.name}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {service.service_templates.name}
                        </p>
                      </div>
                    </div>
                    {getStatusBadge(service.status)}
                  </div>

                  {/* Resource Usage */}
                  {service.status === 'running' && (
                    <div className="grid grid-cols-3 gap-2 mb-3">
                      <div className="text-center">
                        <Cpu className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                        <p className={`text-xs font-medium ${getResourceUsageColor(service.cpu_usage)}`}>
                          {service.cpu_usage?.toFixed(1) || 0}%
                        </p>
                      </div>
                      <div className="text-center">
                        <MemoryStick className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                        <p className={`text-xs font-medium ${getResourceUsageColor(service.memory_usage)}`}>
                          {service.memory_usage?.toFixed(1) || 0}%
                        </p>
                      </div>
                      <div className="text-center">
                        <HardDrive className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                        <p className={`text-xs font-medium ${getResourceUsageColor(service.storage_usage)}`}>
                          {service.storage_usage?.toFixed(1) || 0}%
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Service Info */}
                  <div className="space-y-2 text-xs text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Uptime:</span>
                      <span className="font-medium">{formatUptime(service.uptime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last Check:</span>
                      <span className="font-medium">{formatLastHealthCheck(service.last_health_check)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Created:</span>
                      <span className="font-medium">
                        {new Date(service.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center space-x-2">
                      <button
                        className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title="View Details"
                      >
                        <Eye className="w-3 h-3" />
                      </button>
                      <button
                        className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                        title="Restart Service"
                      >
                        <RotateCcw className="w-3 h-3" />
                      </button>
                      <button
                        className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        title="Delete Service"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                    
                    <div className="flex items-center">
                      <StatusIcon className={`w-4 h-4 ${getStatusColor(service.status)}`} />
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

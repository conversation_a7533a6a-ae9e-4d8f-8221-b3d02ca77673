import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calculator, 
  Users, 
  Database, 
  Globe, 
  Zap,
  Info,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '../ui/Card';

interface ResourceCalculatorProps {
  onResourceChange: (resources: {
    cpu_cores: number;
    memory_mb: number;
    storage_gb: number;
  }) => void;
  serviceType: 'database' | 'application';
  initialResources?: {
    cpu_cores: number;
    memory_mb: number;
    storage_gb: number;
  };
}

interface WorkloadProfile {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  cpu_cores: number;
  memory_mb: number;
  storage_gb: number;
  concurrent_users: number;
  use_case: string;
}

export function ResourceCalculator({ 
  onResourceChange, 
  serviceType, 
  initialResources 
}: ResourceCalculatorProps) {
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [customValues, setCustomValues] = useState({
    concurrent_users: 10,
    data_size_gb: 1,
    traffic_level: 'low'
  });
  const [calculatedResources, setCalculatedResources] = useState({
    cpu_cores: initialResources?.cpu_cores || 0.5,
    memory_mb: initialResources?.memory_mb || 512,
    storage_gb: initialResources?.storage_gb || 1
  });

  const databaseProfiles: WorkloadProfile[] = [
    {
      id: 'development',
      name: 'Development',
      description: 'Untuk development dan testing',
      icon: Zap,
      cpu_cores: 0.5,
      memory_mb: 512,
      storage_gb: 1,
      concurrent_users: 5,
      use_case: 'Development, testing, prototype'
    },
    {
      id: 'small_production',
      name: 'Small Production',
      description: 'Aplikasi kecil dengan traffic rendah',
      icon: Users,
      cpu_cores: 1,
      memory_mb: 1024,
      storage_gb: 5,
      concurrent_users: 50,
      use_case: 'Blog, website personal, aplikasi internal'
    },
    {
      id: 'medium_production',
      name: 'Medium Production',
      description: 'Aplikasi bisnis dengan traffic sedang',
      icon: Globe,
      cpu_cores: 2,
      memory_mb: 2048,
      storage_gb: 20,
      concurrent_users: 200,
      use_case: 'E-commerce kecil, SaaS startup, API backend'
    },
    {
      id: 'high_performance',
      name: 'High Performance',
      description: 'Aplikasi dengan traffic tinggi',
      icon: TrendingUp,
      cpu_cores: 4,
      memory_mb: 4096,
      storage_gb: 50,
      concurrent_users: 1000,
      use_case: 'E-commerce besar, aplikasi enterprise'
    }
  ];

  const applicationProfiles: WorkloadProfile[] = [
    {
      id: 'development',
      name: 'Development',
      description: 'Untuk development dan testing',
      icon: Zap,
      cpu_cores: 0.5,
      memory_mb: 512,
      storage_gb: 2,
      concurrent_users: 5,
      use_case: 'Development environment, testing'
    },
    {
      id: 'small_app',
      name: 'Small Application',
      description: 'Aplikasi kecil dengan resource minimal',
      icon: Users,
      cpu_cores: 1,
      memory_mb: 1024,
      storage_gb: 5,
      concurrent_users: 25,
      use_case: 'WordPress blog, static site generator'
    },
    {
      id: 'medium_app',
      name: 'Medium Application',
      description: 'Aplikasi dengan kebutuhan resource sedang',
      icon: Globe,
      cpu_cores: 2,
      memory_mb: 2048,
      storage_gb: 10,
      concurrent_users: 100,
      use_case: 'CMS, automation tools, business apps'
    },
    {
      id: 'resource_intensive',
      name: 'Resource Intensive',
      description: 'Aplikasi dengan kebutuhan resource tinggi',
      icon: TrendingUp,
      cpu_cores: 4,
      memory_mb: 4096,
      storage_gb: 25,
      concurrent_users: 500,
      use_case: 'Data processing, ML workloads, heavy apps'
    }
  ];

  const profiles = serviceType === 'database' ? databaseProfiles : applicationProfiles;

  useEffect(() => {
    if (selectedProfile) {
      const profile = profiles.find(p => p.id === selectedProfile);
      if (profile) {
        setCalculatedResources({
          cpu_cores: profile.cpu_cores,
          memory_mb: profile.memory_mb,
          storage_gb: profile.storage_gb
        });
      }
    } else {
      calculateCustomResources();
    }
  }, [selectedProfile, customValues, serviceType]);

  useEffect(() => {
    onResourceChange(calculatedResources);
  }, [calculatedResources, onResourceChange]);

  const calculateCustomResources = () => {
    const { concurrent_users, data_size_gb, traffic_level } = customValues;
    
    // Base calculations
    let cpu_cores = 0.5;
    let memory_mb = 512;
    let storage_gb = Math.max(data_size_gb, 1);

    // CPU calculation based on concurrent users
    if (concurrent_users <= 10) {
      cpu_cores = 0.5;
    } else if (concurrent_users <= 50) {
      cpu_cores = 1;
    } else if (concurrent_users <= 200) {
      cpu_cores = 2;
    } else if (concurrent_users <= 500) {
      cpu_cores = 4;
    } else {
      cpu_cores = 8;
    }

    // Memory calculation
    memory_mb = Math.max(512, concurrent_users * 10);
    if (serviceType === 'database') {
      memory_mb = Math.max(memory_mb, data_size_gb * 200); // 200MB per GB of data
    }

    // Traffic level multiplier
    const trafficMultiplier = {
      low: 1,
      medium: 1.5,
      high: 2
    }[traffic_level] || 1;

    cpu_cores *= trafficMultiplier;
    memory_mb *= trafficMultiplier;

    // Round to reasonable values
    cpu_cores = Math.round(cpu_cores * 2) / 2; // Round to nearest 0.5
    memory_mb = Math.ceil(memory_mb / 128) * 128; // Round to nearest 128MB
    storage_gb = Math.max(storage_gb, 1);

    setCalculatedResources({
      cpu_cores: Math.min(cpu_cores, 16), // Cap at 16 cores
      memory_mb: Math.min(memory_mb, 32768), // Cap at 32GB
      storage_gb: Math.min(storage_gb, 1000) // Cap at 1TB
    });
  };

  const selectProfile = (profileId: string) => {
    setSelectedProfile(profileId);
  };

  const useCustomCalculation = () => {
    setSelectedProfile('');
  };

  const getResourceRecommendation = () => {
    const { cpu_cores, memory_mb, storage_gb } = calculatedResources;
    
    if (cpu_cores >= 4 || memory_mb >= 4096) {
      return {
        level: 'high',
        message: 'Konfigurasi high-performance untuk aplikasi dengan traffic tinggi',
        icon: TrendingUp,
        color: 'text-red-600'
      };
    } else if (cpu_cores >= 2 || memory_mb >= 2048) {
      return {
        level: 'medium',
        message: 'Konfigurasi balanced untuk aplikasi production',
        icon: Globe,
        color: 'text-yellow-600'
      };
    } else {
      return {
        level: 'low',
        message: 'Konfigurasi basic untuk development atau aplikasi kecil',
        icon: Zap,
        color: 'text-green-600'
      };
    }
  };

  const recommendation = getResourceRecommendation();
  const RecommendationIcon = recommendation.icon;

  return (
    <div className="space-y-6">
      <div className="flex items-center mb-4">
        <Calculator className="w-5 h-5 text-blue-600 mr-2" />
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          Resource Calculator
        </h4>
      </div>

      {/* Workload Profiles */}
      <div>
        <h5 className="text-md font-medium text-gray-900 dark:text-white mb-3">
          Pilih Profil Workload
        </h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {profiles.map((profile) => {
            const Icon = profile.icon;
            return (
              <Card
                key={profile.id}
                className={`cursor-pointer transition-all ${
                  selectedProfile === profile.id
                    ? 'ring-2 ring-blue-500 border-blue-500'
                    : 'hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => selectProfile(profile.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start">
                    <Icon className="w-5 h-5 text-blue-600 mr-3 mt-0.5" />
                    <div className="flex-1">
                      <h6 className="font-medium text-gray-900 dark:text-white">
                        {profile.name}
                      </h6>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        {profile.description}
                      </p>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        <p>{profile.cpu_cores} CPU • {profile.memory_mb}MB RAM • {profile.storage_gb}GB Storage</p>
                        <p className="mt-1">{profile.use_case}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Custom Calculator */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h5 className="text-md font-medium text-gray-900 dark:text-white">
            Kalkulator Custom
          </h5>
          <button
            onClick={useCustomCalculation}
            className={`text-sm px-3 py-1 rounded-lg transition-colors ${
              selectedProfile === ''
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Gunakan Custom
          </button>
        </div>

        <Card className={selectedProfile !== '' ? 'opacity-50' : ''}>
          <CardContent className="p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Concurrent Users
                </label>
                <input
                  type="number"
                  min="1"
                  value={customValues.concurrent_users}
                  onChange={(e) => setCustomValues(prev => ({ 
                    ...prev, 
                    concurrent_users: Number(e.target.value) 
                  }))}
                  disabled={selectedProfile !== ''}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Size (GB)
                </label>
                <input
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={customValues.data_size_gb}
                  onChange={(e) => setCustomValues(prev => ({ 
                    ...prev, 
                    data_size_gb: Number(e.target.value) 
                  }))}
                  disabled={selectedProfile !== ''}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Traffic Level
                </label>
                <select
                  value={customValues.traffic_level}
                  onChange={(e) => setCustomValues(prev => ({ 
                    ...prev, 
                    traffic_level: e.target.value 
                  }))}
                  disabled={selectedProfile !== ''}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calculated Resources */}
      <Card className="bg-gray-50 dark:bg-gray-900">
        <CardHeader>
          <div className="flex items-center justify-between">
            <h5 className="font-medium text-gray-900 dark:text-white">
              Recommended Resources
            </h5>
            <div className="flex items-center">
              <RecommendationIcon className={`w-4 h-4 mr-1 ${recommendation.color}`} />
              <span className={`text-sm ${recommendation.color}`}>
                {recommendation.level.toUpperCase()}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {calculatedResources.cpu_cores}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">CPU Cores</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {calculatedResources.memory_mb}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">MB RAM</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {calculatedResources.storage_gb}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">GB Storage</div>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-3">
            <div className="flex items-start">
              <Info className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
              <div>
                <p className="text-sm text-blue-900 dark:text-blue-100 font-medium">
                  {recommendation.message}
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  Resource dapat disesuaikan setelah deployment sesuai kebutuhan aktual.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

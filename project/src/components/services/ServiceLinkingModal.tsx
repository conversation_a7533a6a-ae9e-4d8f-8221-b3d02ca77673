import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  X, 
  Link, 
  Database, 
  Server, 
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface ServiceLinkingModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationService: any;
  onSuccess: () => void;
}

interface DatabaseService {
  id: string;
  name: string;
  status: string;
  service_templates: {
    name: string;
    category: string;
  };
  connection_info: any;
  credentials: any;
}

interface ConnectionConfig {
  database_host: string;
  database_port: string;
  database_name: string;
  database_user: string;
  database_password: string;
  connection_string?: string;
}

export function ServiceLinkingModal({ 
  isOpen, 
  onClose, 
  applicationService, 
  onSuccess 
}: ServiceLinkingModalProps) {
  const { profile } = useAuth();
  const [databases, setDatabases] = useState<DatabaseService[]>([]);
  const [selectedDatabase, setSelectedDatabase] = useState<DatabaseService | null>(null);
  const [connectionConfig, setConnectionConfig] = useState<ConnectionConfig>({
    database_host: '',
    database_port: '',
    database_name: '',
    database_user: '',
    database_password: '',
    connection_string: ''
  });
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);

  useEffect(() => {
    if (isOpen) {
      fetchDatabases();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedDatabase) {
      generateConnectionConfig();
    }
  }, [selectedDatabase]);

  const fetchDatabases = async () => {
    try {
      const { data, error } = await supabase
        .from('service_instances')
        .select(`
          *,
          service_templates (name, category)
        `)
        .eq('user_id', profile?.id)
        .eq('status', 'running')
        .in('service_templates.type', ['database']);

      if (error) throw error;
      setDatabases(data || []);
    } catch (error) {
      console.error('Error fetching databases:', error);
      toast.error('Gagal memuat database');
    }
  };

  const generateConnectionConfig = () => {
    if (!selectedDatabase) return;

    const connectionInfo = selectedDatabase.connection_info || {};
    const credentials = selectedDatabase.credentials || {};
    
    const config: ConnectionConfig = {
      database_host: connectionInfo.host || '',
      database_port: connectionInfo.port || '',
      database_name: credentials.database || credentials.POSTGRES_DB || credentials.MYSQL_DATABASE || '',
      database_user: credentials.username || credentials.POSTGRES_USER || credentials.MYSQL_USER || '',
      database_password: credentials.password || credentials.POSTGRES_PASSWORD || credentials.MYSQL_PASSWORD || '',
    };

    // Generate connection string based on database type
    const dbType = selectedDatabase.service_templates.category.toLowerCase();
    if (dbType.includes('postgres')) {
      config.connection_string = `postgresql://${config.database_user}:${config.database_password}@${config.database_host}:${config.database_port}/${config.database_name}`;
    } else if (dbType.includes('mysql')) {
      config.connection_string = `mysql://${config.database_user}:${config.database_password}@${config.database_host}:${config.database_port}/${config.database_name}`;
    } else if (dbType.includes('mongodb')) {
      config.connection_string = `mongodb://${config.database_user}:${config.database_password}@${config.database_host}:${config.database_port}/${config.database_name}`;
    }

    setConnectionConfig(config);
  };

  const createServiceLink = async () => {
    if (!selectedDatabase || !applicationService) return;

    setLoading(true);
    try {
      // Create service link record
      const { error: linkError } = await supabase
        .from('service_links')
        .insert({
          user_id: profile?.id,
          application_id: applicationService.id,
          database_id: selectedDatabase.id,
          connection_config: connectionConfig,
          status: 'active'
        });

      if (linkError) throw linkError;

      // Update application service with database connection info
      const { error: updateError } = await supabase
        .from('service_instances')
        .update({
          environment_vars: {
            ...applicationService.environment_vars,
            ...connectionConfig
          }
        })
        .eq('id', applicationService.id);

      if (updateError) throw updateError;

      toast.success('Database berhasil dihubungkan ke aplikasi');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating service link:', error);
      toast.error(error.message || 'Gagal menghubungkan database');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (step < 2) setStep(step + 1);
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Hubungkan Database
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Hubungkan {applicationService?.name} dengan database
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                1
              </div>
              <span className={`ml-3 text-sm font-medium ${
                step >= 1 ? 'text-gray-900 dark:text-white' : 'text-gray-500'
              }`}>
                Pilih Database
              </span>
            </div>
            
            <div className={`w-16 h-0.5 ${step > 1 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}`} />
            
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                2
              </div>
              <span className={`ml-3 text-sm font-medium ${
                step >= 2 ? 'text-gray-900 dark:text-white' : 'text-gray-500'
              }`}>
                Konfigurasi
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Step 1: Select Database */}
          {step === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-4"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Pilih Database untuk Dihubungkan
              </h3>

              {databases.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Tidak Ada Database
                    </h4>
                    <p className="text-gray-500 dark:text-gray-400">
                      Anda perlu membuat database terlebih dahulu sebelum menghubungkannya dengan aplikasi.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {databases.map((database) => (
                    <Card
                      key={database.id}
                      className={`cursor-pointer transition-all ${
                        selectedDatabase?.id === database.id
                          ? 'ring-2 ring-blue-500 border-blue-500'
                          : 'hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                      onClick={() => setSelectedDatabase(database)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center mb-3">
                          <Database className="w-6 h-6 text-blue-600 mr-3" />
                          <div>
                            <h4 className="font-semibold text-gray-900 dark:text-white">
                              {database.name}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {database.service_templates.name}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
                            <span className="text-sm text-green-600 dark:text-green-400">
                              Running
                            </span>
                          </div>
                          {selectedDatabase?.id === database.id && (
                            <CheckCircle className="w-5 h-5 text-blue-600" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {/* Step 2: Configuration */}
          {step === 2 && selectedDatabase && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Konfigurasi Koneksi
              </h3>

              {/* Connection Preview */}
              <Card className="bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Server className="w-5 h-5 text-blue-600 mr-2" />
                      <span className="font-medium text-blue-900 dark:text-blue-100">
                        {applicationService.name}
                      </span>
                    </div>
                    <ArrowRight className="w-5 h-5 text-blue-600" />
                    <div className="flex items-center">
                      <Database className="w-5 h-5 text-blue-600 mr-2" />
                      <span className="font-medium text-blue-900 dark:text-blue-100">
                        {selectedDatabase.name}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Aplikasi akan dapat mengakses database menggunakan environment variables berikut
                  </p>
                </CardContent>
              </Card>

              {/* Environment Variables */}
              <Card>
                <CardHeader>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    Environment Variables
                  </h4>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Object.entries(connectionConfig).map(([key, value]) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {key.toUpperCase()}
                      </label>
                      <input
                        type={key.includes('password') ? 'password' : 'text'}
                        value={value}
                        onChange={(e) => setConnectionConfig(prev => ({
                          ...prev,
                          [key]: e.target.value
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                        readOnly={key !== 'connection_string'}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Warning */}
              <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                <div className="flex">
                  <AlertCircle className="w-5 h-5 text-yellow-400 mr-2" />
                  <div>
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200">
                      Perhatian
                    </h5>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                      Environment variables akan ditambahkan ke aplikasi dan aplikasi akan di-restart untuk menerapkan perubahan.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={step === 1 ? onClose : prevStep}
            className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            {step === 1 ? (
              <>
                <X className="w-4 h-4 mr-2" />
                Batal
              </>
            ) : (
              <>
                <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                Kembali
              </>
            )}
          </button>

          <div className="flex space-x-3">
            {step < 2 ? (
              <button
                onClick={nextStep}
                disabled={!selectedDatabase}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Lanjut
                <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            ) : (
              <button
                onClick={createServiceLink}
                disabled={loading}
                className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Menghubungkan...
                  </>
                ) : (
                  <>
                    <Link className="w-4 h-4 mr-2" />
                    Hubungkan Database
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

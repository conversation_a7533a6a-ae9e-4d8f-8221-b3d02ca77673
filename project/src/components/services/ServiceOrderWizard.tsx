import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Database, 
  Server, 
  Cpu, 
  HardDrive, 
  MemoryStick,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Calculator
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { supabase, ServiceTemplate } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { formatCurrency } from '../../lib/tripay';
import toast from 'react-hot-toast';

interface ServiceOrderWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface OrderConfig {
  template: ServiceTemplate | null;
  serviceName: string;
  customResources: {
    cpu_cores: number;
    memory_mb: number;
    storage_gb: number;
  };
  billingType: 'hourly' | 'monthly';
  estimatedCost: number;
}

export function ServiceOrderWizard({ isOpen, onClose, onSuccess }: ServiceOrderWizardProps) {
  const { profile } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [templates, setTemplates] = useState<ServiceTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [orderConfig, setOrderConfig] = useState<OrderConfig>({
    template: null,
    serviceName: '',
    customResources: {
      cpu_cores: 0.5,
      memory_mb: 512,
      storage_gb: 1
    },
    billingType: 'hourly',
    estimatedCost: 0
  });

  const steps = [
    { id: 1, title: 'Pilih Layanan', description: 'Pilih template layanan yang diinginkan' },
    { id: 2, title: 'Konfigurasi', description: 'Atur nama dan spesifikasi resource' },
    { id: 3, title: 'Review & Bayar', description: 'Review pesanan dan konfirmasi pembayaran' }
  ];

  useEffect(() => {
    if (isOpen) {
      fetchTemplates();
      resetWizard();
    }
  }, [isOpen]);

  useEffect(() => {
    calculateCost();
  }, [orderConfig.template, orderConfig.customResources, orderConfig.billingType]);

  const fetchTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('service_templates')
        .select('*')
        .eq('status', 'active')
        .order('type', { ascending: true });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Gagal memuat template layanan');
    }
  };

  const resetWizard = () => {
    setCurrentStep(1);
    setOrderConfig({
      template: null,
      serviceName: '',
      customResources: {
        cpu_cores: 0.5,
        memory_mb: 512,
        storage_gb: 1
      },
      billingType: 'hourly',
      estimatedCost: 0
    });
  };

  const calculateCost = () => {
    if (!orderConfig.template) return;

    const { template, customResources, billingType } = orderConfig;
    
    // Calculate resource multiplier based on custom vs default resources
    const cpuMultiplier = customResources.cpu_cores / template.cpu_cores;
    const memoryMultiplier = customResources.memory_mb / template.memory_mb;
    const storageMultiplier = customResources.storage_gb / template.storage_gb;
    
    // Use the highest multiplier for pricing
    const resourceMultiplier = Math.max(cpuMultiplier, memoryMultiplier, storageMultiplier);
    
    const basePrice = billingType === 'hourly' ? template.hourly_price : template.monthly_price;
    const estimatedCost = basePrice * resourceMultiplier;
    
    setOrderConfig(prev => ({ ...prev, estimatedCost }));
  };

  const selectTemplate = (template: ServiceTemplate) => {
    setOrderConfig(prev => ({
      ...prev,
      template,
      serviceName: `${template.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      customResources: {
        cpu_cores: template.cpu_cores,
        memory_mb: template.memory_mb,
        storage_gb: template.storage_gb
      }
    }));
    setCurrentStep(2);
  };

  const updateResource = (resource: keyof OrderConfig['customResources'], value: number) => {
    setOrderConfig(prev => ({
      ...prev,
      customResources: {
        ...prev.customResources,
        [resource]: value
      }
    }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return orderConfig.template !== null;
      case 2:
        return orderConfig.serviceName.trim() !== '';
      case 3:
        return profile && profile.balance >= orderConfig.estimatedCost;
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (canProceed() && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const createService = async () => {
    if (!orderConfig.template || !profile) return;

    setLoading(true);
    try {
      // Check balance
      if (profile.balance < orderConfig.estimatedCost) {
        toast.error('Saldo tidak mencukupi. Silakan top up terlebih dahulu.');
        return;
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/provision-service`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_template_id: orderConfig.template.id,
          service_name: orderConfig.serviceName,
          custom_resources: orderConfig.customResources,
          billing_type: orderConfig.billingType
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Gagal membuat layanan');
      }

      toast.success('Layanan berhasil dibuat dan sedang disiapkan');
      onSuccess();
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Gagal membuat layanan');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Buat Layanan Baru
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {steps[currentStep - 1].description}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id 
                      ? 'text-gray-900 dark:text-white' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id 
                      ? 'bg-blue-600' 
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            {/* Step 1: Template Selection */}
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Pilih Template Layanan
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {templates.map((template) => (
                    <Card 
                      key={template.id} 
                      hover
                      className={`cursor-pointer transition-all ${
                        orderConfig.template?.id === template.id 
                          ? 'ring-2 ring-blue-500 border-blue-500' 
                          : ''
                      }`}
                      onClick={() => selectTemplate(template)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center mb-3">
                          {template.type === 'database' ? (
                            <Database className="w-6 h-6 text-blue-600 mr-2" />
                          ) : (
                            <Server className="w-6 h-6 text-purple-600 mr-2" />
                          )}
                          <div>
                            <h4 className="font-semibold text-gray-900 dark:text-white">
                              {template.name}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {template.category}
                            </p>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                          {template.description}
                        </p>

                        {/* Resource Specs */}
                        <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
                          <div className="text-center">
                            <Cpu className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                            <p>{template.cpu_cores} CPU</p>
                          </div>
                          <div className="text-center">
                            <MemoryStick className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                            <p>{template.memory_mb}MB</p>
                          </div>
                          <div className="text-center">
                            <HardDrive className="w-3 h-3 text-gray-400 mx-auto mb-1" />
                            <p>{template.storage_gb}GB</p>
                          </div>
                        </div>

                        {/* Pricing */}
                        <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="text-xs text-gray-500">Per Jam</p>
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {formatCurrency(template.hourly_price)}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-xs text-gray-500">Per Bulan</p>
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {formatCurrency(template.monthly_price)}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Step 2: Configuration */}
            {currentStep === 2 && orderConfig.template && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Konfigurasi Layanan
                </h3>

                {/* Service Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nama Layanan
                  </label>
                  <input
                    type="text"
                    value={orderConfig.serviceName}
                    onChange={(e) => setOrderConfig(prev => ({ ...prev, serviceName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="my-database-service"
                  />
                </div>

                {/* Resource Configuration */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    Spesifikasi Resource
                  </h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        CPU Cores
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={orderConfig.customResources.cpu_cores}
                        onChange={(e) => updateResource('cpu_cores', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Memory (MB)
                      </label>
                      <input
                        type="number"
                        min="128"
                        step="128"
                        value={orderConfig.customResources.memory_mb}
                        onChange={(e) => updateResource('memory_mb', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Storage (GB)
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={orderConfig.customResources.storage_gb}
                        onChange={(e) => updateResource('storage_gb', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* Billing Type */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    Tipe Billing
                  </h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <Card 
                      className={`cursor-pointer transition-all ${
                        orderConfig.billingType === 'hourly' 
                          ? 'ring-2 ring-blue-500 border-blue-500' 
                          : ''
                      }`}
                      onClick={() => setOrderConfig(prev => ({ ...prev, billingType: 'hourly' }))}
                    >
                      <CardContent className="p-4 text-center">
                        <Clock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                        <h5 className="font-semibold text-gray-900 dark:text-white">Per Jam</h5>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Bayar sesuai penggunaan
                        </p>
                      </CardContent>
                    </Card>

                    <Card 
                      className={`cursor-pointer transition-all ${
                        orderConfig.billingType === 'monthly' 
                          ? 'ring-2 ring-blue-500 border-blue-500' 
                          : ''
                      }`}
                      onClick={() => setOrderConfig(prev => ({ ...prev, billingType: 'monthly' }))}
                    >
                      <CardContent className="p-4 text-center">
                        <Calendar className="w-6 h-6 text-green-600 mx-auto mb-2" />
                        <h5 className="font-semibold text-gray-900 dark:text-white">Per Bulan</h5>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Hemat hingga 30%
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Cost Calculator */}
                <Card className="bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700">
                  <CardContent className="p-4">
                    <div className="flex items-center mb-2">
                      <Calculator className="w-5 h-5 text-blue-600 mr-2" />
                      <h5 className="font-semibold text-blue-900 dark:text-blue-100">
                        Estimasi Biaya
                      </h5>
                    </div>
                    <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                      {formatCurrency(orderConfig.estimatedCost)}
                      <span className="text-sm font-normal ml-1">
                        / {orderConfig.billingType === 'hourly' ? 'jam' : 'bulan'}
                      </span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                      Biaya akan dideduct otomatis dari saldo Anda
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Step 3: Review & Payment */}
            {currentStep === 3 && orderConfig.template && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Review & Konfirmasi
                </h3>

                {/* Order Summary */}
                <Card>
                  <CardHeader>
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      Ringkasan Pesanan
                    </h4>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Template:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.template.name}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Nama Layanan:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.serviceName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">CPU:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.customResources.cpu_cores} cores
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Memory:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.customResources.memory_mb} MB
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Storage:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.customResources.storage_gb} GB
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Billing:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {orderConfig.billingType === 'hourly' ? 'Per Jam' : 'Per Bulan'}
                      </span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <div className="flex justify-between text-lg">
                        <span className="font-semibold text-gray-900 dark:text-white">Total:</span>
                        <span className="font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(orderConfig.estimatedCost)}
                          <span className="text-sm font-normal ml-1">
                            / {orderConfig.billingType === 'hourly' ? 'jam' : 'bulan'}
                          </span>
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Balance Check */}
                <Card className={`${
                  profile && profile.balance >= orderConfig.estimatedCost
                    ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'
                    : 'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {profile && profile.balance >= orderConfig.estimatedCost ? (
                          <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                        ) : (
                          <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                        )}
                        <div>
                          <p className={`font-medium ${
                            profile && profile.balance >= orderConfig.estimatedCost
                              ? 'text-green-900 dark:text-green-100'
                              : 'text-red-900 dark:text-red-100'
                          }`}>
                            Saldo Anda: {formatCurrency(profile?.balance || 0)}
                          </p>
                          <p className={`text-sm ${
                            profile && profile.balance >= orderConfig.estimatedCost
                              ? 'text-green-700 dark:text-green-300'
                              : 'text-red-700 dark:text-red-300'
                          }`}>
                            {profile && profile.balance >= orderConfig.estimatedCost
                              ? 'Saldo mencukupi untuk membuat layanan'
                              : 'Saldo tidak mencukupi. Silakan top up terlebih dahulu.'
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={currentStep === 1 ? onClose : prevStep}
            className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            {currentStep === 1 ? (
              <>
                <X className="w-4 h-4 mr-2" />
                Batal
              </>
            ) : (
              <>
                <ChevronLeft className="w-4 h-4 mr-2" />
                Kembali
              </>
            )}
          </button>

          <div className="flex space-x-3">
            {currentStep < 3 ? (
              <button
                onClick={nextStep}
                disabled={!canProceed()}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Lanjut
                <ChevronRight className="w-4 h-4 ml-2" />
              </button>
            ) : (
              <button
                onClick={createService}
                disabled={!canProceed() || loading}
                className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Membuat...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Buat Layanan
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

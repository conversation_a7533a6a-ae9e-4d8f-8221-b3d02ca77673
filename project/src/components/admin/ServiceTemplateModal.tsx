import React, { useState, useEffect } from 'react';
import { X, Save, Database, Server } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface ServiceTemplate {
  id?: string;
  name: string;
  type: 'database' | 'application';
  category: string;
  version: string;
  docker_image: string;
  docker_config: any;
  cpu_cores: number;
  memory_mb: number;
  storage_gb: number;
  hourly_price: number;
  monthly_price: number;
  description: string;
  features: string[];
  ports: any[];
  environment_vars: any;
  volume_mounts: any[];
  status: 'active' | 'inactive' | 'deprecated';
}

interface ServiceTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: ServiceTemplate | null;
  isEditing: boolean;
  onSave: () => void;
}

export function ServiceTemplateModal({ 
  isOpen, 
  onClose, 
  template, 
  isEditing, 
  onSave 
}: ServiceTemplateModalProps) {
  const [formData, setFormData] = useState<ServiceTemplate>({
    name: '',
    type: 'database',
    category: '',
    version: 'latest',
    docker_image: '',
    docker_config: {},
    cpu_cores: 0.5,
    memory_mb: 512,
    storage_gb: 1,
    hourly_price: 0,
    monthly_price: 0,
    description: '',
    features: [],
    ports: [],
    environment_vars: {},
    volume_mounts: [],
    status: 'active'
  });
  const [featuresText, setFeaturesText] = useState('');
  const [portsText, setPortsText] = useState('');
  const [envVarsText, setEnvVarsText] = useState('');
  const [loading, setSaving] = useState(false);

  useEffect(() => {
    if (template && isEditing) {
      setFormData(template);
      setFeaturesText(template.features.join('\n'));
      setPortsText(JSON.stringify(template.ports, null, 2));
      setEnvVarsText(JSON.stringify(template.environment_vars, null, 2));
    } else {
      // Reset form for new template
      setFormData({
        name: '',
        type: 'database',
        category: '',
        version: 'latest',
        docker_image: '',
        docker_config: {},
        cpu_cores: 0.5,
        memory_mb: 512,
        storage_gb: 1,
        hourly_price: 0,
        monthly_price: 0,
        description: '',
        features: [],
        ports: [],
        environment_vars: {},
        volume_mounts: [],
        status: 'active'
      });
      setFeaturesText('');
      setPortsText('');
      setEnvVarsText('');
    }
  }, [template, isEditing, isOpen]);

  const handleSave = async () => {
    try {
      setSaving(true);

      // Parse features, ports, and environment variables
      const features = featuresText.split('\n').filter(f => f.trim());
      let ports = [];
      let envVars = {};

      try {
        ports = portsText ? JSON.parse(portsText) : [];
      } catch (e) {
        toast.error('Format JSON ports tidak valid');
        return;
      }

      try {
        envVars = envVarsText ? JSON.parse(envVarsText) : {};
      } catch (e) {
        toast.error('Format JSON environment variables tidak valid');
        return;
      }

      const templateData = {
        ...formData,
        features,
        ports,
        environment_vars: envVars
      };

      if (isEditing && template?.id) {
        // Update existing template
        const { error } = await supabase
          .from('service_templates')
          .update(templateData)
          .eq('id', template.id);

        if (error) throw error;
        toast.success('Template berhasil diperbarui');
      } else {
        // Create new template
        const { error } = await supabase
          .from('service_templates')
          .insert([templateData]);

        if (error) throw error;
        toast.success('Template berhasil dibuat');
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Gagal menyimpan template');
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Template' : 'Tambah Template Baru'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <div className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nama Template
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., PostgreSQL 15"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Tipe
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({...formData, type: e.target.value as 'database' | 'application'})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="database">Database</option>
                <option value="application">Application</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Kategori
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., postgresql, mysql, wordpress"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Version
              </label>
              <input
                type="text"
                value={formData.version}
                onChange={(e) => setFormData({...formData, version: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., latest, 15, 8.0"
              />
            </div>
          </div>

          {/* Docker Configuration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Docker Image
            </label>
            <input
              type="text"
              value={formData.docker_image}
              onChange={(e) => setFormData({...formData, docker_image: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="e.g., postgres:15-alpine"
            />
          </div>

          {/* Resource Specifications */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                CPU Cores
              </label>
              <input
                type="number"
                step="0.1"
                value={formData.cpu_cores}
                onChange={(e) => setFormData({...formData, cpu_cores: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Memory (MB)
              </label>
              <input
                type="number"
                value={formData.memory_mb}
                onChange={(e) => setFormData({...formData, memory_mb: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Storage (GB)
              </label>
              <input
                type="number"
                value={formData.storage_gb}
                onChange={(e) => setFormData({...formData, storage_gb: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Harga Per Jam (IDR)
              </label>
              <input
                type="number"
                value={formData.hourly_price}
                onChange={(e) => setFormData({...formData, hourly_price: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Harga Per Bulan (IDR)
              </label>
              <input
                type="number"
                value={formData.monthly_price}
                onChange={(e) => setFormData({...formData, monthly_price: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Deskripsi
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
              placeholder="Deskripsi template layanan..."
            />
          </div>

          {/* Features */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Features (satu per baris)
            </label>
            <textarea
              value={featuresText}
              onChange={(e) => setFeaturesText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={4}
              placeholder="ACID compliant&#10;Advanced indexing&#10;JSON support"
            />
          </div>

          {/* Ports Configuration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Ports Configuration (JSON)
            </label>
            <textarea
              value={portsText}
              onChange={(e) => setPortsText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
              rows={4}
              placeholder='[{"internal": 5432, "name": "postgresql"}]'
            />
          </div>

          {/* Environment Variables */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Environment Variables (JSON)
            </label>
            <textarea
              value={envVarsText}
              onChange={(e) => setEnvVarsText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
              rows={4}
              placeholder='{"POSTGRES_DB": "mydb", "POSTGRES_USER": "user"}'
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({...formData, status: e.target.value as 'active' | 'inactive' | 'deprecated'})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="deprecated">Deprecated</option>
            </select>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Batal
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {loading ? 'Menyimpan...' : 'Simpan'}
          </button>
        </div>
      </div>
    </div>
  );
}

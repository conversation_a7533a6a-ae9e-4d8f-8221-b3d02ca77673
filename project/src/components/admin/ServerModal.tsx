import React, { useState, useEffect } from 'react';
import { X, Save, Server } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface ServerData {
  id?: string;
  name: string;
  hostname: string;
  ip_address: string;
  region: string;
  docker_api_url: string;
  docker_api_key: string;
  cpu_cores: number;
  memory_gb: number;
  storage_gb: number;
  cpu_usage: number;
  memory_usage: number;
  storage_usage: number;
  status: 'active' | 'maintenance' | 'offline' | 'overloaded';
}

interface ServerModalProps {
  isOpen: boolean;
  onClose: () => void;
  server: ServerData | null;
  isEditing: boolean;
  onSave: () => void;
}

export function ServerModal({ 
  isOpen, 
  onClose, 
  server, 
  isEditing, 
  onSave 
}: ServerModalProps) {
  const [formData, setFormData] = useState<ServerData>({
    name: '',
    hostname: '',
    ip_address: '',
    region: 'jakarta',
    docker_api_url: '',
    docker_api_key: '',
    cpu_cores: 4,
    memory_gb: 8,
    storage_gb: 100,
    cpu_usage: 0,
    memory_usage: 0,
    storage_usage: 0,
    status: 'active'
  });
  const [loading, setSaving] = useState(false);

  useEffect(() => {
    if (server && isEditing) {
      setFormData(server);
    } else {
      // Reset form for new server
      setFormData({
        name: '',
        hostname: '',
        ip_address: '',
        region: 'jakarta',
        docker_api_url: '',
        docker_api_key: '',
        cpu_cores: 4,
        memory_gb: 8,
        storage_gb: 100,
        cpu_usage: 0,
        memory_usage: 0,
        storage_usage: 0,
        status: 'active'
      });
    }
  }, [server, isEditing, isOpen]);

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validate required fields
      if (!formData.name || !formData.hostname || !formData.ip_address || !formData.docker_api_url) {
        toast.error('Mohon lengkapi semua field yang wajib diisi');
        return;
      }

      if (isEditing && server?.id) {
        // Update existing server
        const { error } = await supabase
          .from('servers')
          .update(formData)
          .eq('id', server.id);

        if (error) throw error;
        toast.success('Server berhasil diperbarui');
      } else {
        // Create new server
        const { error } = await supabase
          .from('servers')
          .insert([formData]);

        if (error) throw error;
        toast.success('Server berhasil ditambahkan');
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving server:', error);
      toast.error('Gagal menyimpan server');
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Server' : 'Tambah Server Baru'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <div className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nama Server *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., Jakarta Server 1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Hostname *
              </label>
              <input
                type="text"
                value={formData.hostname}
                onChange={(e) => setFormData({...formData, hostname: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., docker-host-1.example.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                IP Address *
              </label>
              <input
                type="text"
                value={formData.ip_address}
                onChange={(e) => setFormData({...formData, ip_address: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., *************"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Region
              </label>
              <select
                value={formData.region}
                onChange={(e) => setFormData({...formData, region: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="jakarta">Jakarta</option>
                <option value="singapore">Singapore</option>
                <option value="sydney">Sydney</option>
                <option value="tokyo">Tokyo</option>
                <option value="mumbai">Mumbai</option>
              </select>
            </div>
          </div>

          {/* Docker Configuration */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Docker API URL *
              </label>
              <input
                type="text"
                value={formData.docker_api_url}
                onChange={(e) => setFormData({...formData, docker_api_url: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., https://docker-host-1.example.com:2376"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Docker API Key
              </label>
              <input
                type="password"
                value={formData.docker_api_key}
                onChange={(e) => setFormData({...formData, docker_api_key: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="API key untuk akses Docker daemon"
              />
            </div>
          </div>

          {/* Resource Specifications */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                CPU Cores
              </label>
              <input
                type="number"
                value={formData.cpu_cores}
                onChange={(e) => setFormData({...formData, cpu_cores: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Memory (GB)
              </label>
              <input
                type="number"
                value={formData.memory_gb}
                onChange={(e) => setFormData({...formData, memory_gb: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Storage (GB)
              </label>
              <input
                type="number"
                value={formData.storage_gb}
                onChange={(e) => setFormData({...formData, storage_gb: Number(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="1"
              />
            </div>
          </div>

          {/* Current Usage (only show when editing) */}
          {isEditing && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  CPU Usage (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.cpu_usage}
                  onChange={(e) => setFormData({...formData, cpu_usage: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0"
                  max="100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Memory Usage (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.memory_usage}
                  onChange={(e) => setFormData({...formData, memory_usage: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0"
                  max="100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Storage Usage (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.storage_usage}
                  onChange={(e) => setFormData({...formData, storage_usage: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0"
                  max="100"
                />
              </div>
            </div>
          )}

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({...formData, status: e.target.value as any})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="offline">Offline</option>
              <option value="overloaded">Overloaded</option>
            </select>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Batal
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {loading ? 'Menyimpan...' : 'Simpan'}
          </button>
        </div>
      </div>
    </div>
  );
}

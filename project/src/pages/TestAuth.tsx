import React, { useState } from 'react';
import { supabase } from '../lib/supabase';
import { toast } from 'react-hot-toast';

export default function TestAuth() {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<any[]>([]);

  const createTestUsers = async () => {
    setLoading(true);
    try {
      // Create admin user
      const { data: adminData, error: adminError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'admin123456',
        options: {
          data: {
            full_name: 'CloudHost Admin'
          }
        }
      });

      if (adminError) {
        console.error('Admin creation error:', adminError);
      } else {
        console.log('Admin created:', adminData);
        
        // Update admin role
        if (adminData.user) {
          const { error: roleError } = await supabase
            .from('profiles')
            .update({ 
              role: 'admin',
              full_name: 'CloudHost Admin',
              balance: 0
            })
            .eq('id', adminData.user.id);
          
          if (roleError) {
            console.error('Error updating admin role:', roleError);
          }
        }
      }

      // Create test user
      const { data: userData, error: userError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'user123456',
        options: {
          data: {
            full_name: 'Test User'
          }
        }
      });

      if (userError) {
        console.error('User creation error:', userError);
      } else {
        console.log('User created:', userData);
        
        // Update user balance
        if (userData.user) {
          const { error: balanceError } = await supabase
            .from('profiles')
            .update({ 
              full_name: 'Test User',
              balance: 100000 // 100k IDR for testing
            })
            .eq('id', userData.user.id);
          
          if (balanceError) {
            console.error('Error updating user balance:', balanceError);
          }
        }
      }

      toast.success('Test users created successfully!');
      await fetchUsers();
    } catch (error) {
      console.error('Error creating test users:', error);
      toast.error('Error creating test users');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching users:', error);
        toast.error('Error fetching users');
      } else {
        setUsers(data || []);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const testLogin = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('Login error:', error);
        toast.error(`Login failed: ${error.message}`);
      } else {
        console.log('Login successful:', data);
        toast.success(`Login successful as ${email}`);
        
        // Fetch user profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError) {
          console.error('Profile error:', profileError);
        } else {
          console.log('User profile:', profile);
        }
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Login error');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Logout error:', error);
        toast.error('Logout failed');
      } else {
        toast.success('Logged out successfully');
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const createSampleService = async () => {
    setLoading(true);
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Please login first');
        return;
      }

      // Get a template and server
      const { data: template } = await supabase
        .from('service_templates')
        .select('*')
        .eq('category', 'postgresql')
        .limit(1)
        .single();

      const { data: server } = await supabase
        .from('servers')
        .select('*')
        .eq('status', 'active')
        .limit(1)
        .single();

      if (!template || !server) {
        toast.error('No template or server available');
        return;
      }

      // Create service instance
      const { data, error } = await supabase
        .from('service_instances')
        .insert({
          user_id: user.id,
          service_template_id: template.id,
          server_id: server.id,
          name: 'test-postgres-db',
          status: 'running',
          cpu_cores: template.cpu_cores,
          memory_mb: template.memory_mb,
          storage_gb: template.storage_gb,
          ports: template.ports,
          environment_vars: {
            POSTGRES_DB: 'testdb',
            POSTGRES_USER: 'testuser',
            POSTGRES_PASSWORD: 'testpass123'
          },
          volume_mounts: template.volume_mounts,
          connection_info: {
            host: server.hostname,
            port: 5432,
            database: 'testdb',
            username: 'testuser',
            password: 'testpass123'
          },
          credentials: {
            POSTGRES_DB: 'testdb',
            POSTGRES_USER: 'testuser',
            POSTGRES_PASSWORD: 'testpass123'
          },
          billing_type: 'hourly',
          hourly_rate: template.hourly_price,
          docker_container_id: 'container_test_postgres',
          last_health_check: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Service creation error:', error);
        toast.error(`Service creation failed: ${error.message}`);
      } else {
        console.log('Service created:', data);
        toast.success('Sample service created successfully!');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error creating service');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            CloudHost ID - Test Authentication
          </h1>

          <div className="space-y-6">
            {/* Create Test Users */}
            <div className="border-b pb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                1. Create Test Users
              </h2>
              <button
                onClick={createTestUsers}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Test Users'}
              </button>
              <p className="text-sm text-gray-600 mt-2">
                Creates <EMAIL> (admin) and <EMAIL> (user) with password: admin123456 / user123456
              </p>
            </div>

            {/* Test Login */}
            <div className="border-b pb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                2. Test Login
              </h2>
              <div className="flex space-x-4">
                <button
                  onClick={() => testLogin('<EMAIL>', 'admin123456')}
                  disabled={loading}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  Login as Admin
                </button>
                <button
                  onClick={() => testLogin('<EMAIL>', 'user123456')}
                  disabled={loading}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  Login as User
                </button>
                <button
                  onClick={logout}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                >
                  Logout
                </button>
              </div>
            </div>

            {/* Create Sample Service */}
            <div className="border-b pb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                3. Create Sample Service
              </h2>
              <button
                onClick={createSampleService}
                disabled={loading}
                className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Sample PostgreSQL Service'}
              </button>
              <p className="text-sm text-gray-600 mt-2">
                Creates a sample PostgreSQL service for the logged-in user
              </p>
            </div>

            {/* Users List */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Current Users
              </h2>
              <button
                onClick={fetchUsers}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 mb-4"
              >
                Refresh Users
              </button>
              
              {users.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Role
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Balance
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.email}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.full_name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.role === 'admin' 
                                ? 'bg-red-100 text-red-800' 
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {user.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Rp {new Intl.NumberFormat('id-ID').format(user.balance)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.status === 'active' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {user.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No users found. Create test users first.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

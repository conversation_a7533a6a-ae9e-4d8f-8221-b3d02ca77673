import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Server, 
  Database, 
  Cpu, 
  MemoryStick, 
  HardDrive,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Eye,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui/Card';
import { SystemMetricsChart } from '../components/monitoring/SystemMetricsChart';
import { ServiceHealthGrid } from '../components/monitoring/ServiceHealthGrid';
import { AlertsPanel } from '../components/monitoring/AlertsPanel';
import { useAuth } from '../contexts/AuthContext';
import { useServiceInstances } from '../hooks/useRealtime';
import { supabase } from '../lib/supabase';

interface SystemMetrics {
  timestamp: string;
  total_services: number;
  running_services: number;
  failed_services: number;
  avg_cpu_usage: number;
  avg_memory_usage: number;
  avg_storage_usage: number;
  total_requests: number;
  response_time: number;
}

interface ServerMetrics {
  id: string;
  name: string;
  status: string;
  cpu_usage: number;
  memory_usage: number;
  storage_usage: number;
  service_count: number;
  last_ping: string;
}

export function Monitoring() {
  const { profile } = useAuth();
  const { data: services, loading: servicesLoading } = useServiceInstances();
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics[]>([]);
  const [serverMetrics, setServerMetrics] = useState<ServerMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  useEffect(() => {
    fetchMetrics();
    
    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      
      // Fetch system metrics (last 24 hours)
      const { data: metricsData } = await supabase
        .from('system_metrics')
        .select('*')
        .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('timestamp', { ascending: true });

      if (metricsData) {
        setSystemMetrics(metricsData);
      }

      // Fetch server metrics
      const { data: serversData } = await supabase
        .from('servers')
        .select(`
          id,
          name,
          status,
          cpu_usage,
          memory_usage,
          storage_usage,
          last_ping,
          service_instances!inner(id)
        `);

      if (serversData) {
        const serverMetricsData = serversData.map(server => ({
          ...server,
          service_count: server.service_instances?.length || 0
        }));
        setServerMetrics(serverMetricsData);
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSystemHealth = () => {
    if (!services || services.length === 0) return 'unknown';
    
    const runningServices = services.filter(s => s.status === 'running').length;
    const healthPercentage = (runningServices / services.length) * 100;
    
    if (healthPercentage >= 95) return 'healthy';
    if (healthPercentage >= 80) return 'warning';
    return 'critical';
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600 dark:text-green-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'critical': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'critical': return XCircle;
      default: return Activity;
    }
  };

  const systemHealth = getSystemHealth();
  const HealthIcon = getHealthIcon(systemHealth);

  const currentMetrics = systemMetrics[systemMetrics.length - 1];
  const previousMetrics = systemMetrics[systemMetrics.length - 2];

  const calculateTrend = (current: number, previous: number) => {
    if (!previous) return 0;
    return ((current - previous) / previous) * 100;
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    const TrendIcon = isPositive ? TrendingUp : TrendingDown;
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <TrendIcon className="w-3 h-3 mr-1" />
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  const overviewCards = [
    {
      title: 'Total Services',
      value: services?.length || 0,
      trend: currentMetrics && previousMetrics ? 
        calculateTrend(currentMetrics.total_services, previousMetrics.total_services) : 0,
      icon: Server,
      color: 'blue'
    },
    {
      title: 'Running Services',
      value: services?.filter(s => s.status === 'running').length || 0,
      trend: currentMetrics && previousMetrics ? 
        calculateTrend(currentMetrics.running_services, previousMetrics.running_services) : 0,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: 'Failed Services',
      value: services?.filter(s => s.status === 'failed').length || 0,
      trend: currentMetrics && previousMetrics ? 
        calculateTrend(currentMetrics.failed_services, previousMetrics.failed_services) : 0,
      icon: XCircle,
      color: 'red'
    },
    {
      title: 'Avg Response Time',
      value: currentMetrics?.response_time || 0,
      unit: 'ms',
      trend: currentMetrics && previousMetrics ? 
        calculateTrend(currentMetrics.response_time, previousMetrics.response_time) : 0,
      icon: Zap,
      color: 'purple'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            System Monitoring
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time monitoring dan analytics platform
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Auto Refresh Toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="autoRefresh" className="text-sm text-gray-700 dark:text-gray-300">
              Auto Refresh
            </label>
          </div>

          {/* Refresh Interval */}
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            className="text-sm px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value={10}>10s</option>
            <option value={30}>30s</option>
            <option value={60}>1m</option>
            <option value={300}>5m</option>
          </select>

          {/* Manual Refresh */}
          <button
            onClick={fetchMetrics}
            disabled={loading}
            className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* System Health Status */}
      <Card className={`border-l-4 ${
        systemHealth === 'healthy' ? 'border-l-green-500' :
        systemHealth === 'warning' ? 'border-l-yellow-500' : 'border-l-red-500'
      }`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <HealthIcon className={`w-6 h-6 mr-3 ${getHealthColor(systemHealth)}`} />
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  System Health: {systemHealth.charAt(0).toUpperCase() + systemHealth.slice(1)}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {services?.filter(s => s.status === 'running').length || 0} of {services?.length || 0} services running
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500 dark:text-gray-400">Last updated</p>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {new Date().toLocaleTimeString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {card.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {card.value}{card.unit && <span className="text-sm ml-1">{card.unit}</span>}
                      </p>
                      {card.trend !== 0 && formatTrend(card.trend)}
                    </div>
                    <div className={`p-3 rounded-full bg-${card.color}-100 dark:bg-${card.color}-900`}>
                      <Icon className={`w-6 h-6 text-${card.color}-600 dark:text-${card.color}-400`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Charts and Detailed Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Metrics Chart */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              System Performance
            </h3>
          </CardHeader>
          <CardContent>
            <SystemMetricsChart data={systemMetrics} />
          </CardContent>
        </Card>

        {/* Server Status */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Server Status
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {serverMetrics.map((server) => (
                <div key={server.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <Server className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {server.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {server.service_count} services
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-500">CPU</p>
                      <p className={`text-sm font-medium ${
                        server.cpu_usage > 80 ? 'text-red-600' : 
                        server.cpu_usage > 60 ? 'text-yellow-600' : 'text-green-600'
                      }`}>
                        {server.cpu_usage.toFixed(1)}%
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">RAM</p>
                      <p className={`text-sm font-medium ${
                        server.memory_usage > 80 ? 'text-red-600' : 
                        server.memory_usage > 60 ? 'text-yellow-600' : 'text-green-600'
                      }`}>
                        {server.memory_usage.toFixed(1)}%
                      </p>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      server.status === 'active' ? 'bg-green-500' :
                      server.status === 'maintenance' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Health Grid and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ServiceHealthGrid services={services || []} />
        </div>
        <div>
          <AlertsPanel />
        </div>
      </div>
    </div>
  );
}

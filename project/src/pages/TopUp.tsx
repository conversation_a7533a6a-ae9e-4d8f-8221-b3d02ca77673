import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  QrCode,
  Clock,
  CheckCircle,
  XCircle,
  ExternalLink,
  Copy
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { useAuth } from '../contexts/AuthContext';
import { useBalance } from '../hooks/useRealtime';
import { supabase, TripayTransaction } from '../lib/supabase';
import { TRIPAY_PAYMENT_METHODS, calculateFee, formatCurrency } from '../lib/tripay';
import toast from 'react-hot-toast';

const topUpSchema = z.object({
  amount: z.number().min(10000, 'Minimal top-up Rp 10.000').max(50000000, 'Maksimal top-up Rp 50.000.000'),
  paymentMethod: z.string().min(1, 'Pilih metode pembayaran')
});

type TopUpForm = z.infer<typeof topUpSchema>;

export function TopUp() {
  const { profile } = useAuth();
  const balance = useBalance();
  const [loading, setLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState<any>(null);
  const [transactions, setTransactions] = useState<TripayTransaction[]>([]);
  const [loadingTransactions, setLoadingTransactions] = useState(true);

  const form = useForm<TopUpForm>({
    resolver: zodResolver(topUpSchema),
    defaultValues: {
      amount: 50000,
      paymentMethod: ''
    }
  });

  const watchedAmount = form.watch('amount');
  const watchedPaymentMethod = form.watch('paymentMethod');
  const selectedMethod = TRIPAY_PAYMENT_METHODS.find(m => m.code === watchedPaymentMethod);
  const fee = selectedMethod ? calculateFee(watchedAmount || 0, watchedPaymentMethod) : 0;
  const totalAmount = (watchedAmount || 0) + fee;

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    if (!profile) return;

    try {
      const { data, error } = await supabase
        .from('tripay_transactions')
        .select('*')
        .eq('user_id', profile.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setTransactions(data || []);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setLoadingTransactions(false);
    }
  };

  const onSubmit = async (data: TopUpForm) => {
    if (!profile) return;

    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-tripay-payment`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: data.amount,
          payment_method: data.paymentMethod,
          customer_name: profile.full_name || profile.email,
          customer_email: profile.email,
          customer_phone: profile.phone || '08123456789',
          order_items: [
            {
              name: 'Top Up Saldo CloudHost ID',
              quantity: 1,
              price: data.amount
            }
          ]
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Gagal membuat pembayaran');
      }

      setPaymentData(result.data);
      setShowPaymentModal(true);
      form.reset();
      fetchTransactions();
    } catch (error: any) {
      toast.error(error.message || 'Gagal membuat pembayaran');
    } finally {
      setLoading(false);
    }
  };

  const quickAmounts = [50000, 100000, 250000, 500000, 1000000, 2500000];

  const getPaymentIcon = (category: string) => {
    switch (category) {
      case 'Virtual Account':
        return <Building className="w-5 h-5" />;
      case 'E-Wallet':
        return <Smartphone className="w-5 h-5" />;
      case 'QR Code':
        return <QrCode className="w-5 h-5" />;
      case 'Retail':
        return <Building className="w-5 h-5" />;
      default:
        return <CreditCard className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'text-green-600 dark:text-green-400';
      case 'UNPAID':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'EXPIRED':
        return 'text-red-600 dark:text-red-400';
      case 'FAILED':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'UNPAID':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'EXPIRED':
      case 'FAILED':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Disalin ke clipboard');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Top Up Saldo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Isi saldo untuk membayar layanan hosting Anda
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Up Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <CreditCard className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                Tambah Saldo
              </h2>
            </CardHeader>
            <CardContent>
              {/* Current Balance */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 mb-6">
                <div className="text-sm text-gray-600 dark:text-gray-400">Saldo Saat Ini</div>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {formatCurrency(balance)}
                </div>
              </div>

              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Amount Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Jumlah Top Up
                  </label>
                  
                  {/* Quick Amount Buttons */}
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {quickAmounts.map((amount) => (
                      <button
                        key={amount}
                        type="button"
                        onClick={() => form.setValue('amount', amount)}
                        className={`p-2 text-sm border rounded-lg transition-all ${
                          watchedAmount === amount
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        }`}
                      >
                        {formatCurrency(amount)}
                      </button>
                    ))}
                  </div>

                  {/* Custom Amount Input */}
                  <input
                    type="number"
                    {...form.register('amount', { valueAsNumber: true })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Atau masukkan jumlah custom"
                  />
                  {form.formState.errors.amount && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {form.formState.errors.amount.message}
                    </p>
                  )}
                </div>

                {/* Payment Methods */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Metode Pembayaran
                  </label>
                  
                  {/* Group by category */}
                  {['Virtual Account', 'E-Wallet', 'QR Code', 'Retail'].map((category) => {
                    const methods = TRIPAY_PAYMENT_METHODS.filter(m => m.category === category);
                    if (methods.length === 0) return null;

                    return (
                      <div key={category} className="mb-4">
                        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 flex items-center">
                          {getPaymentIcon(category)}
                          <span className="ml-2">{category}</span>
                        </h4>
                        <div className="grid grid-cols-1 gap-2">
                          {methods.map((method) => (
                            <label
                              key={method.code}
                              className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                                watchedPaymentMethod === method.code
                                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                              }`}
                            >
                              <input
                                type="radio"
                                {...form.register('paymentMethod')}
                                value={method.code}
                                className="sr-only"
                              />
                              <span className="text-xl mr-3">{method.icon}</span>
                              <div className="flex-1">
                                <span className="font-medium text-gray-900 dark:text-white">
                                  {method.name}
                                </span>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                  Fee: {method.fee_percent}% + {formatCurrency(method.fee_flat)}
                                </div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                  
                  {form.formState.errors.paymentMethod && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {form.formState.errors.paymentMethod.message}
                    </p>
                  )}
                </div>

                {/* Payment Summary */}
                {watchedAmount && selectedMethod && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                      Ringkasan Pembayaran
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Jumlah Top Up:</span>
                        <span className="text-gray-900 dark:text-white">{formatCurrency(watchedAmount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Biaya Admin:</span>
                        <span className="text-gray-900 dark:text-white">{formatCurrency(fee)}</span>
                      </div>
                      <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                        <div className="flex justify-between font-medium">
                          <span className="text-gray-900 dark:text-white">Total Bayar:</span>
                          <span className="text-gray-900 dark:text-white">{formatCurrency(totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  type="submit"
                  loading={loading}
                  disabled={!watchedAmount || !watchedPaymentMethod}
                  className="w-full"
                  size="lg"
                >
                  Buat Pembayaran
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Transaction History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Riwayat Transaksi
              </h2>
            </CardHeader>
            <CardContent>
              {loadingTransactions ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : transactions.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    Belum ada transaksi top-up
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(transaction.status)}
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {formatCurrency(transaction.amount)}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {transaction.payment_name}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500">
                            {new Date(transaction.created_at).toLocaleString('id-ID')}
                          </p>
                        </div>
                      </div>
                      <span className={`text-sm font-medium ${getStatusColor(transaction.status)}`}>
                        {transaction.status}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Payment Modal */}
      {paymentData && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setPaymentData(null);
          }}
          paymentData={paymentData}
          onCopy={copyToClipboard}
        />
      )}
    </div>
  );
}

// Payment Modal Component
function PaymentModal({
  isOpen,
  onClose,
  paymentData,
  onCopy
}: {
  isOpen: boolean;
  onClose: () => void;
  paymentData: any;
  onCopy: (text: string) => void;
}) {
  const [timeRemaining, setTimeRemaining] = useState('');

  useEffect(() => {
    if (!paymentData.expired_time) return;

    const updateTimer = () => {
      const now = new Date().getTime();
      const expiry = paymentData.expired_time * 1000;
      const diff = expiry - now;

      if (diff <= 0) {
        setTimeRemaining('Expired');
        return;
      }

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeRemaining(`${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [paymentData.expired_time]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Pembayaran" size="lg">
      <div className="space-y-6">
        {/* Payment Info */}
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {formatCurrency(paymentData.amount)}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            via {paymentData.payment_method}
          </div>
          {timeRemaining && timeRemaining !== 'Expired' && (
            <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300">
              <Clock className="w-4 h-4 mr-2" />
              Berlaku hingga: {timeRemaining}
            </div>
          )}
        </div>

        {/* Payment Code */}
        {paymentData.pay_code && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="text-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Kode Pembayaran
              </label>
              <div className="text-2xl font-mono font-bold text-gray-900 dark:text-white mb-3">
                {paymentData.pay_code}
              </div>
              <Button
                size="sm"
                variant="secondary"
                onClick={() => onCopy(paymentData.pay_code)}
              >
                <Copy className="w-4 h-4 mr-2" />
                Salin Kode
              </Button>
            </div>
          </div>
        )}

        {/* Instructions */}
        {paymentData.instructions && paymentData.instructions.length > 0 && (
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-3">
              Cara Pembayaran:
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600 dark:text-gray-400">
              {paymentData.instructions.map((instruction: any, index: number) => (
                <li key={index}>
                  {instruction.title}: {instruction.steps?.join(', ') || instruction.title}
                </li>
              ))}
            </ol>
          </div>
        )}

        {/* Payment URL */}
        {paymentData.checkout_url && (
          <div className="text-center">
            <Button
              onClick={() => window.open(paymentData.checkout_url, '_blank')}
              className="w-full"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Bayar Sekarang
            </Button>
          </div>
        )}

        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Saldo akan otomatis bertambah setelah pembayaran berhasil diverifikasi</p>
        </div>
      </div>
    </Modal>
  );
}
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Database,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Server,
  Cpu,
  HardDrive,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { ServiceTemplateModal } from '../../components/admin/ServiceTemplateModal';
import { supabase } from '../../lib/supabase';
import { formatCurrency } from '../../lib/tripay';
import toast from 'react-hot-toast';

interface ServiceTemplate {
  id: string;
  name: string;
  type: 'database' | 'application';
  category: string;
  version: string;
  docker_image: string;
  docker_config: any;
  cpu_cores: number;
  memory_mb: number;
  storage_gb: number;
  hourly_price: number;
  monthly_price: number;
  description: string;
  features: string[];
  ports: any[];
  environment_vars: any;
  volume_mounts: any[];
  status: 'active' | 'inactive' | 'deprecated';
  created_at: string;
  updated_at: string;
}

export function ServiceTemplateManagement() {
  const [templates, setTemplates] = useState<ServiceTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'database' | 'application'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'deprecated'>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<ServiceTemplate | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('service_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Gagal memuat template layanan');
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || template.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || template.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const updateTemplateStatus = async (templateId: string, newStatus: 'active' | 'inactive' | 'deprecated') => {
    try {
      const { error } = await supabase
        .from('service_templates')
        .update({ status: newStatus })
        .eq('id', templateId);

      if (error) throw error;

      setTemplates(templates.map(template => 
        template.id === templateId ? { ...template, status: newStatus } : template
      ));

      toast.success(`Status template berhasil diubah ke ${newStatus}`);
    } catch (error) {
      console.error('Error updating template status:', error);
      toast.error('Gagal mengubah status template');
    }
  };

  const deleteTemplate = async (templateId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus template ini?')) return;

    try {
      const { error } = await supabase
        .from('service_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;

      setTemplates(templates.filter(template => template.id !== templateId));
      toast.success('Template berhasil dihapus');
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Gagal menghapus template');
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      deprecated: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[status as keyof typeof styles]}`}>
        {status}
      </span>
    );
  };

  const getTypeBadge = (type: string) => {
    const styles = {
      database: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      application: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[type as keyof typeof styles]}`}>
        {type}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Template Layanan
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola template layanan dan pricing
          </p>
        </div>
        
        <button
          onClick={() => {
            setSelectedTemplate(null);
            setIsEditing(false);
            setShowTemplateModal(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Tambah Template
        </button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Cari template berdasarkan nama, kategori, atau deskripsi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            </div>

            {/* Type Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="all">Semua Tipe</option>
                <option value="database">Database</option>
                <option value="application">Application</option>
              </select>
            </div>

            {/* Status Filter */}
            <div className="relative">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="all">Semua Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="deprecated">Deprecated</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card hover>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    {template.type === 'database' ? (
                      <Database className="w-6 h-6 text-blue-600" />
                    ) : (
                      <Server className="w-6 h-6 text-purple-600" />
                    )}
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {template.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {template.category} v{template.version}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    {getTypeBadge(template.type)}
                    {getStatusBadge(template.status)}
                  </div>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                  {template.description}
                </p>

                {/* Resource Specs */}
                <div className="grid grid-cols-3 gap-2 mb-4">
                  <div className="text-center">
                    <Cpu className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-500">{template.cpu_cores} CPU</p>
                  </div>
                  <div className="text-center">
                    <Server className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-500">{template.memory_mb}MB RAM</p>
                  </div>
                  <div className="text-center">
                    <HardDrive className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-500">{template.storage_gb}GB</p>
                  </div>
                </div>

                {/* Pricing */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mb-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Per Jam</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(template.hourly_price)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Per Bulan</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(template.monthly_price)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedTemplate(template);
                        setIsEditing(true);
                        setShowTemplateModal(true);
                      }}
                      className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      title="Edit Template"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => deleteTemplate(template.id)}
                      className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      title="Delete Template"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  <button
                    onClick={() => updateTemplateStatus(
                      template.id, 
                      template.status === 'active' ? 'inactive' : 'active'
                    )}
                    className={`p-1 ${
                      template.status === 'active' 
                        ? 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300'
                        : 'text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300'
                    }`}
                    title={template.status === 'active' ? 'Deactivate' : 'Activate'}
                  >
                    {template.status === 'active' ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Tidak ada template ditemukan
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Coba ubah filter pencarian atau tambah template baru.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Template Modal */}
      <ServiceTemplateModal
        isOpen={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        template={selectedTemplate}
        isEditing={isEditing}
        onSave={fetchTemplates}
      />
    </div>
  );
}

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Server, 
  Database, 
  CreditCard,
  TrendingUp,
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { supabase } from '../../lib/supabase';
import { formatCurrency } from '../../lib/tripay';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalServices: number;
  runningServices: number;
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
}

export function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalServices: 0,
    runningServices: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
    systemHealth: 'healthy'
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAdminStats();
  }, []);

  const fetchAdminStats = async () => {
    try {
      // Get user statistics
      const { data: users } = await supabase
        .from('profiles')
        .select('id, status, created_at');

      const totalUsers = users?.length || 0;
      const activeUsers = users?.filter(u => u.status === 'active').length || 0;

      // Get service statistics
      const { data: services } = await supabase
        .from('service_instances')
        .select('id, status');

      const totalServices = services?.length || 0;
      const runningServices = services?.filter(s => s.status === 'running').length || 0;

      // Get revenue statistics
      const { data: transactions } = await supabase
        .from('transactions')
        .select('amount, type, created_at')
        .eq('type', 'topup');

      const totalRevenue = transactions?.reduce((sum, t) => sum + t.amount, 0) || 0;
      
      const currentMonth = new Date();
      currentMonth.setDate(1);
      const monthlyRevenue = transactions?.filter(t => 
        new Date(t.created_at) >= currentMonth
      ).reduce((sum, t) => sum + t.amount, 0) || 0;

      // Get pending payments
      const { data: pendingPayments } = await supabase
        .from('tripay_transactions')
        .select('id')
        .eq('status', 'UNPAID');

      setStats({
        totalUsers,
        activeUsers,
        totalServices,
        runningServices,
        totalRevenue,
        monthlyRevenue,
        pendingPayments: pendingPayments?.length || 0,
        systemHealth: 'healthy' // This would be calculated based on various metrics
      });
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Total Pengguna',
      value: stats.totalUsers,
      subtitle: `${stats.activeUsers} aktif`,
      icon: Users,
      color: 'blue',
      trend: '+12%'
    },
    {
      title: 'Total Layanan',
      value: stats.totalServices,
      subtitle: `${stats.runningServices} berjalan`,
      icon: Server,
      color: 'green',
      trend: '+8%'
    },
    {
      title: 'Revenue Total',
      value: formatCurrency(stats.totalRevenue),
      subtitle: `${formatCurrency(stats.monthlyRevenue)} bulan ini`,
      icon: DollarSign,
      color: 'purple',
      trend: '+15%'
    },
    {
      title: 'Pembayaran Pending',
      value: stats.pendingPayments,
      subtitle: 'Menunggu pembayaran',
      icon: Clock,
      color: 'orange',
      trend: '-5%'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola platform CloudHost ID
          </p>
        </div>
        
        {/* System Health Indicator */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            stats.systemHealth === 'healthy' ? 'bg-green-500' :
            stats.systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            System {stats.systemHealth === 'healthy' ? 'Healthy' : 
                   stats.systemHealth === 'warning' ? 'Warning' : 'Critical'}
          </span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {stat.value}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {stat.subtitle}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900`}>
                      <Icon className={`w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600 dark:text-green-400">
                      {stat.trend}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                      vs bulan lalu
                    </span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Quick Actions
            </h3>
          </CardHeader>
          <CardContent className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="flex items-center">
                <Users className="w-5 h-5 text-blue-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Kelola Pengguna</p>
                  <p className="text-sm text-gray-500">Lihat dan kelola akun pengguna</p>
                </div>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-green-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Template Layanan</p>
                  <p className="text-sm text-gray-500">Kelola template dan pricing</p>
                </div>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="flex items-center">
                <Server className="w-5 h-5 text-purple-600 mr-3" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Server Management</p>
                  <p className="text-sm text-gray-500">Monitor dan kelola server</p>
                </div>
              </div>
            </button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Activity
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">
                    New user registered
                  </p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center">
                <AlertTriangle className="w-4 h-4 text-yellow-500 mr-3" />
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">
                    Server load high
                  </p>
                  <p className="text-xs text-gray-500">5 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center">
                <CreditCard className="w-4 h-4 text-blue-500 mr-3" />
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">
                    Payment received
                  </p>
                  <p className="text-xs text-gray-500">10 minutes ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

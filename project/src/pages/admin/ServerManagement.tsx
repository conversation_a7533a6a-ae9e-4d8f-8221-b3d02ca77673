import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Server, 
  Search, 
  Filter, 
  Plus,
  Edit,
  Trash2,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Cpu,
  HardDrive,
  MemoryStick,
  MapPin,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { ServerModal } from '../../components/admin/ServerModal';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface ServerData {
  id: string;
  name: string;
  hostname: string;
  ip_address: string;
  region: string;
  docker_api_url: string;
  docker_api_key: string;
  cpu_cores: number;
  memory_gb: number;
  storage_gb: number;
  cpu_usage: number;
  memory_usage: number;
  storage_usage: number;
  status: 'active' | 'maintenance' | 'offline' | 'overloaded';
  last_ping: string;
  created_at: string;
  updated_at: string;
  service_count?: number;
}

export function ServerManagement() {
  const [servers, setServers] = useState<ServerData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'maintenance' | 'offline' | 'overloaded'>('all');
  const [selectedServer, setSelectedServer] = useState<ServerData | null>(null);
  const [showServerModal, setShowServerModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchServers();
    // Set up real-time monitoring
    const interval = setInterval(fetchServers, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchServers = async () => {
    try {
      setLoading(true);
      
      const { data: serversData } = await supabase
        .from('servers')
        .select('*')
        .order('created_at', { ascending: false });

      if (serversData) {
        // Get service count for each server
        const serversWithStats = await Promise.all(
          serversData.map(async (server) => {
            const { data: services } = await supabase
              .from('service_instances')
              .select('id')
              .eq('server_id', server.id)
              .eq('status', 'running');

            return {
              ...server,
              service_count: services?.length || 0
            };
          })
        );

        setServers(serversWithStats);
      }
    } catch (error) {
      console.error('Error fetching servers:', error);
      toast.error('Gagal memuat data server');
    } finally {
      setLoading(false);
    }
  };

  const filteredServers = servers.filter(server => {
    const matchesSearch = server.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         server.hostname.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         server.ip_address.includes(searchTerm) ||
                         server.region.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || server.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const updateServerStatus = async (serverId: string, newStatus: 'active' | 'maintenance' | 'offline' | 'overloaded') => {
    try {
      const { error } = await supabase
        .from('servers')
        .update({ status: newStatus })
        .eq('id', serverId);

      if (error) throw error;

      setServers(servers.map(server => 
        server.id === serverId ? { ...server, status: newStatus } : server
      ));

      toast.success(`Status server berhasil diubah ke ${newStatus}`);
    } catch (error) {
      console.error('Error updating server status:', error);
      toast.error('Gagal mengubah status server');
    }
  };

  const deleteServer = async (serverId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus server ini? Semua layanan yang berjalan akan terpengaruh.')) return;

    try {
      const { error } = await supabase
        .from('servers')
        .delete()
        .eq('id', serverId);

      if (error) throw error;

      setServers(servers.filter(server => server.id !== serverId));
      toast.success('Server berhasil dihapus');
    } catch (error) {
      console.error('Error deleting server:', error);
      toast.error('Gagal menghapus server');
    }
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      maintenance: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      offline: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      overloaded: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };

    const icons = {
      active: CheckCircle,
      maintenance: AlertTriangle,
      offline: XCircle,
      overloaded: AlertTriangle
    };

    const Icon = icons[status as keyof typeof icons];

    return (
      <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${styles[status as keyof typeof styles]}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </span>
    );
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'text-red-600 dark:text-red-400';
    if (usage >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const formatLastPing = (lastPing: string) => {
    const now = new Date();
    const ping = new Date(lastPing);
    const diffMs = now.getTime() - ping.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Server Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor dan kelola Docker host servers
          </p>
        </div>
        
        <button
          onClick={() => {
            setSelectedServer(null);
            setIsEditing(false);
            setShowServerModal(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Tambah Server
        </button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Cari berdasarkan nama, hostname, IP, atau region..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="all">Semua Status</option>
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="offline">Offline</option>
                <option value="overloaded">Overloaded</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Servers Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredServers.map((server, index) => (
          <motion.div
            key={server.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card hover>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Server className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {server.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {server.hostname}
                      </p>
                    </div>
                  </div>
                  {getStatusBadge(server.status)}
                </div>

                {/* Server Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <MapPin className="w-4 h-4 mr-2" />
                    {server.region} • {server.ip_address}
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <Activity className="w-4 h-4 mr-2" />
                    {server.service_count} layanan aktif
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <Clock className="w-4 h-4 mr-2" />
                    Last ping: {formatLastPing(server.last_ping)}
                  </div>
                </div>

                {/* Resource Usage */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Cpu className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">CPU</span>
                    </div>
                    <span className={`text-sm font-medium ${getUsageColor(server.cpu_usage)}`}>
                      {server.cpu_usage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        server.cpu_usage >= 90 ? 'bg-red-500' :
                        server.cpu_usage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(server.cpu_usage, 100)}%` }}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MemoryStick className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">Memory</span>
                    </div>
                    <span className={`text-sm font-medium ${getUsageColor(server.memory_usage)}`}>
                      {server.memory_usage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        server.memory_usage >= 90 ? 'bg-red-500' :
                        server.memory_usage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(server.memory_usage, 100)}%` }}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <HardDrive className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">Storage</span>
                    </div>
                    <span className={`text-sm font-medium ${getUsageColor(server.storage_usage)}`}>
                      {server.storage_usage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        server.storage_usage >= 90 ? 'bg-red-500' :
                        server.storage_usage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(server.storage_usage, 100)}%` }}
                    />
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedServer(server);
                        setIsEditing(true);
                        setShowServerModal(true);
                      }}
                      className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      title="Edit Server"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => deleteServer(server.id)}
                      className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      title="Delete Server"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  <select
                    value={server.status}
                    onChange={(e) => updateServerStatus(server.id, e.target.value as any)}
                    className="text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="active">Active</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="offline">Offline</option>
                    <option value="overloaded">Overloaded</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredServers.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Tidak ada server ditemukan
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Coba ubah filter pencarian atau tambah server baru.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Server Modal */}
      <ServerModal
        isOpen={showServerModal}
        onClose={() => setShowServerModal(false)}
        server={selectedServer}
        isEditing={isEditing}
        onSave={fetchServers}
      />
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Save, 
  Mail, 
  CreditCard, 
  Shield, 
  Globe,
  Key,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '../../components/ui/Card';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface SystemConfig {
  // General Settings
  site_name: string;
  site_description: string;
  support_email: string;
  maintenance_mode: boolean;
  
  // Tripay Configuration
  tripay_merchant_code: string;
  tripay_api_key: string;
  tripay_private_key: string;
  tripay_sandbox_mode: boolean;
  
  // Email Configuration
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  smtp_from_email: string;
  smtp_from_name: string;
  
  // Security Settings
  max_login_attempts: number;
  session_timeout: number;
  password_min_length: number;
  require_email_verification: boolean;
  
  // Billing Settings
  default_currency: string;
  tax_rate: number;
  billing_cycle_days: number;
  auto_suspend_days: number;
}

export function SystemConfiguration() {
  const [config, setConfig] = useState<SystemConfig>({
    site_name: 'CloudHost ID',
    site_description: 'Platform SaaS untuk Database & App Provisioning',
    support_email: '<EMAIL>',
    maintenance_mode: false,
    
    tripay_merchant_code: '',
    tripay_api_key: '',
    tripay_private_key: '',
    tripay_sandbox_mode: true,
    
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    smtp_from_email: '',
    smtp_from_name: 'CloudHost ID',
    
    max_login_attempts: 5,
    session_timeout: 24,
    password_min_length: 8,
    require_email_verification: true,
    
    default_currency: 'IDR',
    tax_rate: 11,
    billing_cycle_days: 30,
    auto_suspend_days: 7
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [showPasswords, setShowPasswords] = useState({
    tripay_api_key: false,
    tripay_private_key: false,
    smtp_password: false
  });

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would fetch from a system_config table
      // For now, we'll use default values
      setConfig(config);
    } catch (error) {
      console.error('Error fetching config:', error);
      toast.error('Gagal memuat konfigurasi sistem');
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      
      // In a real implementation, this would save to a system_config table
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast.success('Konfigurasi sistem berhasil disimpan');
    } catch (error) {
      console.error('Error saving config:', error);
      toast.error('Gagal menyimpan konfigurasi sistem');
    } finally {
      setSaving(false);
    }
  };

  const testTripayConnection = async () => {
    try {
      toast.loading('Testing Tripay connection...');
      
      // In a real implementation, this would test the Tripay API connection
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      toast.dismiss();
      toast.success('Koneksi Tripay berhasil!');
    } catch (error) {
      toast.dismiss();
      toast.error('Gagal terhubung ke Tripay API');
    }
  };

  const testEmailConnection = async () => {
    try {
      toast.loading('Testing email connection...');
      
      // In a real implementation, this would test the SMTP connection
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      toast.dismiss();
      toast.success('Koneksi email berhasil!');
    } catch (error) {
      toast.dismiss();
      toast.error('Gagal terhubung ke server email');
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const tabs = [
    { id: 'general', name: 'General', icon: Globe },
    { id: 'tripay', name: 'Tripay', icon: CreditCard },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'billing', name: 'Billing', icon: Settings }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            System Configuration
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola pengaturan sistem dan integrasi
          </p>
        </div>
        
        <button
          onClick={saveConfig}
          disabled={saving}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          {saving ? 'Menyimpan...' : 'Simpan Konfigurasi'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                    >
                      <Icon className="w-4 h-4 mr-3" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-6">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    General Settings
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Site Name
                      </label>
                      <input
                        type="text"
                        value={config.site_name}
                        onChange={(e) => setConfig({...config, site_name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Support Email
                      </label>
                      <input
                        type="email"
                        value={config.support_email}
                        onChange={(e) => setConfig({...config, support_email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Site Description
                    </label>
                    <textarea
                      value={config.site_description}
                      onChange={(e) => setConfig({...config, site_description: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="maintenance_mode"
                      checked={config.maintenance_mode}
                      onChange={(e) => setConfig({...config, maintenance_mode: e.target.checked})}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="maintenance_mode" className="ml-2 block text-sm text-gray-900 dark:text-white">
                      Maintenance Mode
                    </label>
                  </div>
                  
                  {config.maintenance_mode && (
                    <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                      <div className="flex">
                        <AlertTriangle className="w-5 h-5 text-yellow-400 mr-2" />
                        <p className="text-sm text-yellow-800 dark:text-yellow-200">
                          Maintenance mode akan mencegah pengguna mengakses platform. Hanya admin yang dapat masuk.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Tripay Settings */}
              {activeTab === 'tripay' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Tripay Configuration
                    </h3>
                    <button
                      onClick={testTripayConnection}
                      className="flex items-center px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Test Connection
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Merchant Code
                      </label>
                      <input
                        type="text"
                        value={config.tripay_merchant_code}
                        onChange={(e) => setConfig({...config, tripay_merchant_code: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="T1234"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="tripay_sandbox"
                        checked={config.tripay_sandbox_mode}
                        onChange={(e) => setConfig({...config, tripay_sandbox_mode: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="tripay_sandbox" className="ml-2 block text-sm text-gray-900 dark:text-white">
                        Sandbox Mode
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      API Key
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.tripay_api_key ? 'text' : 'password'}
                        value={config.tripay_api_key}
                        onChange={(e) => setConfig({...config, tripay_api_key: e.target.value})}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('tripay_api_key')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showPasswords.tripay_api_key ? (
                          <EyeOff className="w-4 h-4 text-gray-400" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Private Key
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.tripay_private_key ? 'text' : 'password'}
                        value={config.tripay_private_key}
                        onChange={(e) => setConfig({...config, tripay_private_key: e.target.value})}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('tripay_private_key')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showPasswords.tripay_private_key ? (
                          <EyeOff className="w-4 h-4 text-gray-400" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Email Settings */}
              {activeTab === 'email' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Email Configuration
                    </h3>
                    <button
                      onClick={testEmailConnection}
                      className="flex items-center px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Test Connection
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Host
                      </label>
                      <input
                        type="text"
                        value={config.smtp_host}
                        onChange={(e) => setConfig({...config, smtp_host: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="smtp.gmail.com"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Port
                      </label>
                      <input
                        type="number"
                        value={config.smtp_port}
                        onChange={(e) => setConfig({...config, smtp_port: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Username
                      </label>
                      <input
                        type="text"
                        value={config.smtp_username}
                        onChange={(e) => setConfig({...config, smtp_username: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.smtp_password ? 'text' : 'password'}
                          value={config.smtp_password}
                          onChange={(e) => setConfig({...config, smtp_password: e.target.value})}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('smtp_password')}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          {showPasswords.smtp_password ? (
                            <EyeOff className="w-4 h-4 text-gray-400" />
                          ) : (
                            <Eye className="w-4 h-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        From Email
                      </label>
                      <input
                        type="email"
                        value={config.smtp_from_email}
                        onChange={(e) => setConfig({...config, smtp_from_email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        From Name
                      </label>
                      <input
                        type="text"
                        value={config.smtp_from_name}
                        onChange={(e) => setConfig({...config, smtp_from_name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Security Settings
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Max Login Attempts
                      </label>
                      <input
                        type="number"
                        value={config.max_login_attempts}
                        onChange={(e) => setConfig({...config, max_login_attempts: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="1"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Session Timeout (hours)
                      </label>
                      <input
                        type="number"
                        value={config.session_timeout}
                        onChange={(e) => setConfig({...config, session_timeout: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="1"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Minimum Password Length
                      </label>
                      <input
                        type="number"
                        value={config.password_min_length}
                        onChange={(e) => setConfig({...config, password_min_length: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="6"
                      />
                    </div>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="require_email_verification"
                      checked={config.require_email_verification}
                      onChange={(e) => setConfig({...config, require_email_verification: e.target.checked})}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="require_email_verification" className="ml-2 block text-sm text-gray-900 dark:text-white">
                      Require Email Verification
                    </label>
                  </div>
                </div>
              )}

              {/* Billing Settings */}
              {activeTab === 'billing' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Billing Settings
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Default Currency
                      </label>
                      <select
                        value={config.default_currency}
                        onChange={(e) => setConfig({...config, default_currency: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="IDR">IDR (Indonesian Rupiah)</option>
                        <option value="USD">USD (US Dollar)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Tax Rate (%)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={config.tax_rate}
                        onChange={(e) => setConfig({...config, tax_rate: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Billing Cycle (days)
                      </label>
                      <input
                        type="number"
                        value={config.billing_cycle_days}
                        onChange={(e) => setConfig({...config, billing_cycle_days: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="1"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Auto Suspend After (days)
                      </label>
                      <input
                        type="number"
                        value={config.auto_suspend_days}
                        onChange={(e) => setConfig({...config, auto_suspend_days: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        min="1"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

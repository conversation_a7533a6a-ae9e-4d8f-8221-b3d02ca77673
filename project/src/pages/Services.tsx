import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Server, 
  Database, 
  ExternalLink, 
  Settings, 
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Play,
  Square,
  Eye,
  EyeOff
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { ServiceOrderWizard } from '../components/services/ServiceOrderWizard';
import { useServiceInstances } from '../hooks/useRealtime';
import { supabase, ServiceTemplate } from '../lib/supabase';
import { formatCurrency } from '../lib/tripay';
import toast from 'react-hot-toast';

export function Services() {
  const { services, loading, refetch } = useServiceInstances();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedService, setSelectedService] = useState<any>(null);
  const [loadingAction, setLoadingAction] = useState<string | null>(null);
  const [showCredentials, setShowCredentials] = useState<{ [key: string]: boolean }>({});



  const terminateService = async (serviceId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus layanan ini?')) return;

    setLoadingAction(serviceId);
    try {
      const { error } = await supabase
        .from('service_instances')
        .update({ 
          status: 'terminated',
          terminated_at: new Date().toISOString()
        })
        .eq('id', serviceId);

      if (error) throw error;

      toast.success('Layanan berhasil dihapus');
      refetch();
    } catch (error: any) {
      toast.error(error.message || 'Gagal menghapus layanan');
    } finally {
      setLoadingAction(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'pending':
      case 'provisioning':
        return <Clock className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'stopped':
        return <Square className="w-5 h-5 text-gray-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running':
        return 'Berjalan';
      case 'pending':
        return 'Menunggu';
      case 'provisioning':
        return 'Menyiapkan';
      case 'failed':
        return 'Gagal';
      case 'stopped':
        return 'Dihentikan';
      case 'terminated':
        return 'Dihapus';
      default:
        return status;
    }
  };

  const toggleCredentialsVisibility = (serviceId: string) => {
    setShowCredentials(prev => ({
      ...prev,
      [serviceId]: !prev[serviceId]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Disalin ke clipboard');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Layanan Saya
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola database dan aplikasi yang sedang berjalan
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Buat Layanan Baru
        </Button>
      </div>

      {/* Services Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : services.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Server className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Belum Ada Layanan
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Mulai dengan membuat database atau aplikasi pertama Anda
            </p>
            <Button onClick={() => setShowCreateModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Buat Layanan Pertama
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card hover className="h-full">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {service.service_templates?.type === 'database' ? (
                          <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <Server className="w-6 h-6 text-green-600 dark:text-green-400" />
                        )}
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {service.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {service.service_templates?.name}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-4">
                      {/* Status */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(service.status)}
                          <span className="text-sm font-medium">
                            {getStatusText(service.status)}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatCurrency(service.service_templates?.hourly_price || 0)}/jam
                        </span>
                      </div>

                      {/* Connection Info for Running Services */}
                      {service.status === 'running' && service.domain && (
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Akses:
                            </span>
                            <button
                              onClick={() => window.open(`https://${service.domain}`, '_blank')}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </button>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 break-all">
                            {service.domain}
                          </p>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex space-x-2 pt-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => {
                            setSelectedService(service);
                            setShowDetailModal(true);
                          }}
                          className="flex-1"
                        >
                          <Settings className="w-4 h-4 mr-1" />
                          Detail
                        </Button>
                        
                        {service.status !== 'terminated' && (
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => terminateService(service.id)}
                            loading={loadingAction === service.id}
                            disabled={loadingAction === service.id}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Service Order Wizard */}
      <ServiceOrderWizard
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={refetch}
      />

      {/* Service Detail Modal */}
      {selectedService && (
        <ServiceDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedService(null);
          }}
          service={selectedService}
          showCredentials={showCredentials[selectedService.id] || false}
          onToggleCredentials={() => toggleCredentialsVisibility(selectedService.id)}
          onCopyToClipboard={copyToClipboard}
        />
      )}
    </div>
  );
}

// Service Detail Modal Component
function ServiceDetailModal({
  isOpen,
  onClose,
  service,
  showCredentials,
  onToggleCredentials,
  onCopyToClipboard
}: {
  isOpen: boolean;
  onClose: () => void;
  service: any;
  showCredentials: boolean;
  onToggleCredentials: () => void;
  onCopyToClipboard: (text: string) => void;
}) {
  const connectionInfo = service.connection_info || {};
  const credentials = service.credentials || {};

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Detail: ${service.name}`} size="lg">
      <div className="space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Template
            </label>
            <p className="text-gray-900 dark:text-white">
              {service.service_templates?.name}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <div className="flex items-center space-x-2">
              {getStatusIcon(service.status)}
              <span className="text-gray-900 dark:text-white">
                {getStatusText(service.status)}
              </span>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Dibuat
            </label>
            <p className="text-gray-900 dark:text-white">
              {new Date(service.created_at).toLocaleDateString('id-ID')}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Biaya per Jam
            </label>
            <p className="text-green-600 dark:text-green-400 font-medium">
              {formatCurrency(service.service_templates?.hourly_price || 0)}
            </p>
          </div>
        </div>

        {/* Connection Info */}
        {service.status === 'running' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Informasi Koneksi
              </h3>
              <Button
                size="sm"
                variant="secondary"
                onClick={onToggleCredentials}
              >
                {showCredentials ? (
                  <>
                    <EyeOff className="w-4 h-4 mr-2" />
                    Sembunyikan
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Tampilkan
                  </>
                )}
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {connectionInfo.host && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Host
                  </label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono">
                      {connectionInfo.host}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onCopyToClipboard(connectionInfo.host)}
                    >
                      Salin
                    </Button>
                  </div>
                </div>
              )}

              {connectionInfo.port && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Port
                  </label>
                  <code className="block px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono">
                    {connectionInfo.port}
                  </code>
                </div>
              )}

              {showCredentials && Object.keys(credentials).length > 0 && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Kredensial
                  </h4>
                  <div className="space-y-3">
                    {Object.entries(credentials).map(([key, value]) => (
                      <div key={key}>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          {key.replace(/_/g, ' ').toUpperCase()}
                        </label>
                        <div className="flex items-center space-x-2">
                          <code className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono">
                            {value as string}
                          </code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onCopyToClipboard(value as string)}
                          >
                            Salin
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Resource Usage */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Spesifikasi Resource
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">CPU</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {service.service_templates?.cpu_cores} Core
              </p>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">RAM</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {service.service_templates?.memory_mb}MB
              </p>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Storage</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {service.service_templates?.storage_gb}GB
              </p>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
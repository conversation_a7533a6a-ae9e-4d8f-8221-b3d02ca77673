// Tripay payment methods configuration
export const TRIPAY_PAYMENT_METHODS = [
  // Virtual Accounts
  {
    code: 'BRI<PERSON>',
    name: 'Bank BRI',
    category: 'Virtual Account',
    icon: '🏧',
    fee_percent: 0.7,
    fee_flat: 4000,
    min_amount: 10000,
    max_amount: *********
  },
  {
    code: 'BCAVA',
    name: 'Bank BCA',
    category: 'Virtual Account', 
    icon: '🏧',
    fee_percent: 0.7,
    fee_flat: 4000,
    min_amount: 10000,
    max_amount: *********
  },
  {
    code: 'BNIVA',
    name: 'Bank BNI',
    category: 'Virtual Account',
    icon: '🏧',
    fee_percent: 0.7,
    fee_flat: 4000,
    min_amount: 10000,
    max_amount: *********
  },
  {
    code: 'MANDIRIVA',
    name: 'Bank Mandiri',
    category: 'Virtual Account',
    icon: '🏧',
    fee_percent: 0.7,
    fee_flat: 4000,
    min_amount: 10000,
    max_amount: *********
  },
  {
    code: 'BSIVA',
    name: 'Bank Syariah Indonesia',
    category: 'Virtual Account',
    icon: '🏧',
    fee_percent: 0.7,
    fee_flat: 4000,
    min_amount: 10000,
    max_amount: *********
  },
  // E-Wallets
  {
    code: 'OVO',
    name: 'O<PERSON>',
    category: 'E-Wallet',
    icon: '📱',
    fee_percent: 2.0,
    fee_flat: 0,
    min_amount: 10000,
    max_amount: ********
  },
  {
    code: 'DANA',
    name: 'DANA',
    category: 'E-Wallet',
    icon: '📱',
    fee_percent: 2.0,
    fee_flat: 0,
    min_amount: 10000,
    max_amount: ********
  },
  {
    code: 'SHOPEEPAY',
    name: 'ShopeePay',
    category: 'E-Wallet',
    icon: '📱',
    fee_percent: 2.0,
    fee_flat: 0,
    min_amount: 10000,
    max_amount: ********
  },
  {
    code: 'LINKAJA',
    name: 'LinkAja',
    category: 'E-Wallet',
    icon: '📱',
    fee_percent: 2.0,
    fee_flat: 0,
    min_amount: 10000,
    max_amount: ********
  },
  // QRIS
  {
    code: 'QRIS',
    name: 'QRIS',
    category: 'QR Code',
    icon: '📱',
    fee_percent: 0.7,
    fee_flat: 0,
    min_amount: 1500,
    max_amount: 2000000
  },
  // Retail
  {
    code: 'ALFAMART',
    name: 'Alfamart',
    category: 'Retail',
    icon: '🏪',
    fee_percent: 2.5,
    fee_flat: 5000,
    min_amount: 10000,
    max_amount: 2500000
  },
  {
    code: 'INDOMARET',
    name: 'Indomaret',
    category: 'Retail',
    icon: '🏪',
    fee_percent: 2.5,
    fee_flat: 5000,
    min_amount: 10000,
    max_amount: 2500000
  }
];

export function calculateFee(amount: number, paymentMethod: string): number {
  const method = TRIPAY_PAYMENT_METHODS.find(m => m.code === paymentMethod);
  if (!method) return 0;

  const percentageFee = (amount * method.fee_percent) / 100;
  return percentageFee + method.fee_flat;
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('id-ID').format(num);
}
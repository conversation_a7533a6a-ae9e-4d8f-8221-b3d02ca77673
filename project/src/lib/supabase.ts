import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface Profile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'user' | 'admin';
  balance: number;
  status: 'active' | 'suspended' | 'banned';
  created_at: string;
  updated_at: string;
}

export interface ServiceTemplate {
  id: string;
  name: string;
  type: 'database' | 'application';
  category: string;
  version: string;
  docker_image: string;
  docker_config: any;
  cpu_cores: number;
  memory_mb: number;
  storage_gb: number;
  hourly_price: number;
  monthly_price: number;
  description?: string;
  features: string[];
  ports: Array<{ internal: number; name: string }>;
  environment_vars: any;
  volume_mounts: any[];
  status: 'active' | 'inactive' | 'deprecated';
  created_at: string;
  updated_at: string;
}

export interface ServiceInstance {
  id: string;
  user_id: string;
  service_template_id: string;
  server_id: string;
  name: string;
  status: 'pending' | 'provisioning' | 'running' | 'stopped' | 'failed' | 'terminated';
  container_id?: string;
  container_name?: string;
  internal_port?: number;
  external_port?: number;
  domain?: string;
  ssl_enabled: boolean;
  credentials: any;
  connection_info: any;
  environment_vars: any;
  resource_limits: any;
  last_billed: string;
  expires_at?: string;
  terminated_at?: string;
  created_at: string;
  updated_at: string;
  service_templates?: ServiceTemplate;
}

export interface TripayTransaction {
  id: string;
  user_id: string;
  reference: string;
  merchant_ref: string;
  payment_method: string;
  payment_name: string;
  amount: number;
  fee: number;
  total_amount: number;
  status: 'UNPAID' | 'PAID' | 'EXPIRED' | 'FAILED';
  pay_code?: string;
  pay_url?: string;
  checkout_url?: string;
  expired_time?: string;
  paid_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  type: 'topup' | 'deduction' | 'refund' | 'adjustment';
  amount: number;
  balance_before: number;
  balance_after: number;
  description: string;
  tripay_transaction_id?: string;
  service_instance_id?: string;
  created_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data: any;
  read: boolean;
  created_at: string;
}
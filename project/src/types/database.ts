export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      notifications: {
        Row: {
          created_at: string | null
          data: Json | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          balance: number | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          last_login: string | null
          phone: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          balance?: number | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          last_login?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          balance?: number | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          last_login?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      servers: {
        Row: {
          cpu_cores: number
          cpu_usage: number | null
          created_at: string | null
          docker_api_key: string | null
          docker_api_url: string
          hostname: string
          id: string
          ip_address: unknown
          last_ping: string | null
          memory_gb: number
          memory_usage: number | null
          name: string
          region: string
          status: Database["public"]["Enums"]["server_status"] | null
          storage_gb: number
          storage_usage: number | null
          updated_at: string | null
        }
        Insert: {
          cpu_cores: number
          cpu_usage?: number | null
          created_at?: string | null
          docker_api_key?: string | null
          docker_api_url: string
          hostname: string
          id?: string
          ip_address: unknown
          last_ping?: string | null
          memory_gb: number
          memory_usage?: number | null
          name: string
          region: string
          status?: Database["public"]["Enums"]["server_status"] | null
          storage_gb: number
          storage_usage?: number | null
          updated_at?: string | null
        }
        Update: {
          cpu_cores?: number
          cpu_usage?: number | null
          created_at?: string | null
          docker_api_key?: string | null
          docker_api_url?: string
          hostname?: string
          id?: string
          ip_address?: unknown
          last_ping?: string | null
          memory_gb?: number
          memory_usage?: number | null
          name?: string
          region?: string
          status?: Database["public"]["Enums"]["server_status"] | null
          storage_gb?: number
          storage_usage?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      service_instances: {
        Row: {
          billing_type: string | null
          connection_info: Json | null
          cpu_cores: number
          created_at: string | null
          credentials: Json | null
          docker_container_id: string | null
          environment_vars: Json | null
          expires_at: string | null
          hourly_rate: number
          id: string
          last_billed: string | null
          last_health_check: string | null
          memory_mb: number
          monthly_rate: number | null
          name: string
          ports: Json | null
          server_id: string
          service_template_id: string
          status: Database["public"]["Enums"]["service_status"] | null
          storage_gb: number
          terminated_at: string | null
          updated_at: string | null
          uptime_seconds: number | null
          user_id: string
          volume_mounts: Json | null
        }
        Insert: {
          billing_type?: string | null
          connection_info?: Json | null
          cpu_cores: number
          created_at?: string | null
          credentials?: Json | null
          docker_container_id?: string | null
          environment_vars?: Json | null
          expires_at?: string | null
          hourly_rate: number
          id?: string
          last_billed?: string | null
          last_health_check?: string | null
          memory_mb: number
          monthly_rate?: number | null
          name: string
          ports?: Json | null
          server_id: string
          service_template_id: string
          status?: Database["public"]["Enums"]["service_status"] | null
          storage_gb: number
          terminated_at?: string | null
          updated_at?: string | null
          uptime_seconds?: number | null
          user_id: string
          volume_mounts?: Json | null
        }
        Update: {
          billing_type?: string | null
          connection_info?: Json | null
          cpu_cores?: number
          created_at?: string | null
          credentials?: Json | null
          docker_container_id?: string | null
          environment_vars?: Json | null
          expires_at?: string | null
          hourly_rate?: number
          id?: string
          last_billed?: string | null
          last_health_check?: string | null
          memory_mb?: number
          monthly_rate?: number | null
          name?: string
          ports?: Json | null
          server_id?: string
          service_template_id?: string
          status?: Database["public"]["Enums"]["service_status"] | null
          storage_gb?: number
          terminated_at?: string | null
          updated_at?: string | null
          uptime_seconds?: number | null
          user_id?: string
          volume_mounts?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "service_instances_server_id_fkey"
            columns: ["server_id"]
            isOneToOne: false
            referencedRelation: "servers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_instances_service_template_id_fkey"
            columns: ["service_template_id"]
            isOneToOne: false
            referencedRelation: "service_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_instances_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      service_links: {
        Row: {
          application_id: string
          connection_config: Json
          created_at: string | null
          database_id: string
          id: string
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          application_id: string
          connection_config: Json
          created_at?: string | null
          database_id: string
          id?: string
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          application_id?: string
          connection_config?: Json
          created_at?: string | null
          database_id?: string
          id?: string
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_links_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "service_instances"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_links_database_id_fkey"
            columns: ["database_id"]
            isOneToOne: false
            referencedRelation: "service_instances"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_links_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      service_templates: {
        Row: {
          category: string
          cpu_cores: number
          created_at: string | null
          description: string | null
          docker_config: Json | null
          docker_image: string
          environment_vars: Json | null
          features: string[] | null
          hourly_price: number
          id: string
          memory_mb: number
          monthly_price: number
          name: string
          ports: Json | null
          status: Database["public"]["Enums"]["template_status"] | null
          storage_gb: number
          type: Database["public"]["Enums"]["template_type"]
          updated_at: string | null
          version: string | null
          volume_mounts: Json | null
        }
        Insert: {
          category: string
          cpu_cores?: number
          created_at?: string | null
          description?: string | null
          docker_config?: Json | null
          docker_image: string
          environment_vars?: Json | null
          features?: string[] | null
          hourly_price: number
          id?: string
          memory_mb?: number
          monthly_price: number
          name: string
          ports?: Json | null
          status?: Database["public"]["Enums"]["template_status"] | null
          storage_gb?: number
          type: Database["public"]["Enums"]["template_type"]
          updated_at?: string | null
          version?: string | null
          volume_mounts?: Json | null
        }
        Update: {
          category?: string
          cpu_cores?: number
          created_at?: string | null
          description?: string | null
          docker_config?: Json | null
          docker_image?: string
          environment_vars?: Json | null
          features?: string[] | null
          hourly_price?: number
          id?: string
          memory_mb?: number
          monthly_price?: number
          name?: string
          ports?: Json | null
          status?: Database["public"]["Enums"]["template_status"] | null
          storage_gb?: number
          type?: Database["public"]["Enums"]["template_type"]
          updated_at?: string | null
          version?: string | null
          volume_mounts?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_profile: {
        Args: Record<PropertyKey, never>
        Returns: {
          balance: number | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          last_login: string | null
          phone: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          status: string | null
          updated_at: string | null
        }
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      update_user_balance: {
        Args: {
          user_id: string
          amount: number
          transaction_type: Database["public"]["Enums"]["transaction_type"]
          description?: string
        }
        Returns: boolean
      }
    }
    Enums: {
      payment_status: "pending" | "completed" | "failed" | "cancelled"
      server_status: "active" | "maintenance" | "offline" | "overloaded"
      service_status: "pending" | "running" | "stopped" | "failed" | "suspended"
      template_status: "active" | "inactive" | "deprecated"
      template_type: "database" | "application"
      transaction_type: "topup" | "deduction" | "refund" | "adjustment"
      user_role: "user" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Type helpers
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
